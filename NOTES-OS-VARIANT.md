# OS Variant Configuration untuk OKD 4.19 Installation

## Fedora 42 OS Variant

Untuk Fedora 42, gunakan `--os-variant=rhel9.0` karena:

1. **Fedora 42 belum tersedia** di daftar os-variant virt-install
2. **RHEL 9.0 kompatibel** dengan Fedora 42 dalam hal virtualization features
3. **Fedora CoreOS** yang digunakan OKD 4.19 berbasis Fedora 42

## Cara Cek OS Variant yang Tersedia

```bash
# List semua os-variant yang tersedia
osinfo-query os

# Cari Fedora variants
osinfo-query os | grep -i fedora

# Cari RHEL variants
osinfo-query os | grep -i rhel
```

## Rekomendasi OS Variant untuk Berbagai OS

### Untuk Bastion VM (Fedora 42)
```bash
--os-variant=rhel9.0
# atau
--os-variant=fedora41  # jika tersedia
```

### Untuk Cluster VMs (Fedora CoreOS)
```bash
--os-variant=rhel9.0
# Karena FCOS berbasis Fedora yang kompatibel dengan RHEL 9
```

## Mengapa Tidak Pakai `--os-type=linux`?

Parameter `--os-type=linux` sudah **deprecated** dan tidak direkomendasikan karena:

1. **Kurang spesifik** - tidak memberikan optimasi yang tepat
2. **Deprecated** - akan dihapus di versi virt-install yang akan datang
3. **Performa suboptimal** - tidak menggunakan driver dan features yang optimal

## Alternative OS Variants untuk FCOS

Jika `rhel9.0` tidak bekerja dengan baik, coba urutan berikut:

1. `rhel9.0` (recommended)
2. `fedora41`
3. `fedora40`
4. `rhel8.0`
5. `centos8`

## Contoh Penggunaan yang Benar

### Bastion VM
```bash
sudo virt-install \
  --name okd-bastion-server \
  --ram 4096 \
  --vcpus 2 \
  --disk path=/var/lib/libvirt/images/okd-bastion-server.qcow2 \
  --os-variant rhel9.0 \
  --network bridge=openshift4,model=virtio \
  --graphics none \
  --serial pty \
  --console pty \
  --boot hd \
  --import
```

### Cluster VMs
```bash
sudo virt-install -n bootstrap.okd4.febryan.web.id.example \
  --description "Bootstrap Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=4 \
  --os-variant=rhel9.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:a4:db:5f
```

## Troubleshooting OS Variant Issues

### Error: "Unknown OS variant"
```bash
# Cek os-variant yang tersedia
osinfo-query os | grep -E "(fedora|rhel|centos)"

# Gunakan yang paling mendekati
--os-variant=rhel9.0
```

### Error: "Unsupported OS type"
```bash
# Jangan gunakan --os-type=linux
# Gunakan --os-variant yang spesifik
--os-variant=rhel9.0
```

### Performance Issues
```bash
# Pastikan menggunakan virtio drivers
--network bridge=openshift4,model=virtio
--disk bus=virtio

# Dan os-variant yang tepat
--os-variant=rhel9.0
```

## Verifikasi Konfigurasi

Setelah VM dibuat, verifikasi:

```bash
# Cek VM info
sudo virsh dominfo vm-name

# Cek network driver
sudo virsh dumpxml vm-name | grep -A5 interface

# Cek disk driver
sudo virsh dumpxml vm-name | grep -A5 disk
```

## Update untuk Fedora Versions Baru

Ketika Fedora 42 tersedia di osinfo-db:

```bash
# Update osinfo database
sudo dnf update osinfo-db

# Cek availability
osinfo-query os | grep fedora42

# Gunakan jika tersedia
--os-variant=fedora42
```

## Kesimpulan

- **Gunakan `--os-variant=rhel9.0`** untuk Fedora 42 dan FCOS
- **Jangan gunakan `--os-type=linux`** (deprecated)
- **Selalu gunakan virtio drivers** untuk performa optimal
- **Update osinfo-db** secara berkala untuk support OS terbaru
