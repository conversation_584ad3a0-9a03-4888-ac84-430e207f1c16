This guide shows how to provision new Fedora CoreOS (FCOS) instances on a [libvirt](https://libvirt.org/) platform, using the [QEMU](https://www.qemu.org/) hypervisor.

## [](https://docs.fedoraproject.org/en-US/fedora-coreos/provisioning-libvirt/#_prerequisites)Prerequisites

Before provisioning an FCOS machine, you must have an Ignition configuration file containing your customizations. If you do not have one, see [Producing an Ignition File](https://docs.fedoraproject.org/en-US/fedora-coreos/producing-ign/).

<table><tbody><tr><td><i title="Note"></i></td><td>Fedora CoreOS has a default <code>core</code> user that can be used to explore the OS. If you want to use it, finalize its <a href="https://docs.fedoraproject.org/en-US/fedora-coreos/authentication/" class="page">configuration</a> by providing e.g. an SSH key.</td></tr></tbody></table>

You also need to have access to a host machine with `libvirt`. The examples below use the `virt-install` command-line tool, which must be separately installed beforehand.

<table><tbody><tr><td><i title="Tip"></i></td><td>If running on a host with SELinux enabled (use the <code>sestatus</code> command to check SELinux status), make sure your OS image and Ignition file are labeled as <code>svirt_home_t</code>. You can do this by placing them under <code>~/.local/share/libvirt/images/</code> or running <code>chcon -t svirt_home_t /path/to/file</code>.</td></tr></tbody></table>

## [](https://docs.fedoraproject.org/en-US/fedora-coreos/provisioning-libvirt/#_launching_a_vm_instance)Launching a VM instance

1.  Fetch the latest image suitable for the `qemu` platform using `coreos-installer` (or [download and verify](https://fedoraproject.org/coreos/download/) it from the web). You can use `coreos-installer` [as a container](https://quay.io/repository/coreos/coreos-installer), or on Fedora install it from the repos.
    
    ```
    STREAM="stable"
    # as an installed binary:
    coreos-installer download -s $STREAM -p qemu -f qcow2.xz --decompress -C ~/.local/share/libvirt/images/
    # or as a container:
    podman run --pull=always --rm -v "${HOME}/.local/share/libvirt/images/:/data" -w /data \
        quay.io/coreos/coreos-installer:release download -s $STREAM -p qemu -f qcow2.xz --decompress
    ```
    
    bash
    
2.  Launch a new machine via `virt-install`, using the Ignition file with your customizations.
    
    ```
    IGNITION_CONFIG="/path/to/example.ign"
    IMAGE="/path/to/image.qcow2"
    VM_NAME="fcos-test-01"
    VCPUS="2"
    RAM_MB="2048"
    STREAM="stable"
    DISK_GB="10"
    # For x86 / aarch64,
    IGNITION_DEVICE_ARG=(--qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}")
    
    # For s390x / ppc64le,
    IGNITION_DEVICE_ARG=(--disk path="${IGNITION_CONFIG}",format=raw,readonly=on,serial=ignition,startup_policy=optional)
    
    # Setup the correct SELinux label to allow access to the config
    chcon --verbose --type svirt_home_t ${IGNITION_CONFIG}
    
    virt-install --connect="qemu:///system" --name="${VM_NAME}" --vcpus="${VCPUS}" --memory="${RAM_MB}" \
            --os-variant="fedora-coreos-$STREAM" --import --graphics=none \
            --disk="size=${DISK_GB},backing_store=${IMAGE}" \
            --network bridge=virbr0 "${IGNITION_DEVICE_ARG[@]}"
    ```
    
    bash
    

<table><tbody><tr><td><i title="Note"></i></td><td><code>virt-install</code> requires both the OS image and Ignition file to be specified as absolute paths.</td></tr></tbody></table>

<table><tbody><tr><td><i title="Note"></i></td><td>Depending on your version of <code>virt-install</code>, you may not be able to use <code>--os-variant=fedora-coreos-*</code> and will get an error. In this case, you should pick an older Fedora variant (<code>--os-variant=fedora31</code> for example). You can find the variants that are supported by you current version of <code>virt-install</code> with <code>osinfo-query os | grep '^\s*fedora'</code>.</td></tr></tbody></table>

<table><tbody><tr><td><i title="Note"></i></td><td><code>DISK_GB</code> should be at least as big as the default size of the image. For Fedora CoreOS, this is currently 10 GB.</td></tr></tbody></table>

<table><tbody><tr><td><i title="Tip"></i></td><td>Make sure that your user has access to <code>/dev/kvm</code>. The default is to allow access for everyone, but on some distributions you may need to add yourself to the <code>kvm</code> group.</td></tr></tbody></table>

<table><tbody><tr><td><i title="Tip"></i></td><td>You can escape out of the serial console by pressing <code>CTRL + ]</code>.</td></tr></tbody></table>

If you set up an [SSH key](https://docs.fedoraproject.org/en-US/fedora-coreos/authentication/) for the default `core` user, you can SSH into the VM and explore the OS:

```
ssh core@<ip address>
```

bash