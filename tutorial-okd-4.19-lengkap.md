# Tutorial Lengkap: Install OKD 4.19 dengan Fedora CoreOS di KVM

## Daftar Isi
1. [Persiapan Environment](#persiapan-environment)
2. [Setup KVM Infrastructure](#setup-kvm-infrastructure)
3. [Buat Bastion VM](#buat-bastion-vm)
4. [Install Ansible dan Konfigurasi](#install-ansible-dan-konfigurasi)
5. [Setup DHCP Server](#setup-dhcp-server)
6. [Setup DNS Server](#setup-dns-server)
7. [Setup TFTP/PXE Server](#setup-tftppxe-server)
8. [Setup HAProxy Load Balancer](#setup-haproxy-load-balancer)
9. [Download OKD Installer dan CLI](#download-okd-installer-dan-cli)
10. [Generate Ignition Files](#generate-ignition-files)
11. [Buat Cluster VMs](#buat-cluster-vms)
12. [Install OKD Cluster](#install-okd-cluster)

## Spesifikasi Environment

### Network Configuration
- **Network Range**: *************/24
- **Gateway**: *************
- **Bastion IP**: ***************
- **Domain**: febryan.web.id.example
- **Cluster Name**: okd4

### VM Requirements
| VM Type | vCPU | RAM | Storage | IP Address |
|---------|------|-----|---------|------------|
| Bastion | 2 | 4GB | 20GB | *************** |
| Bootstrap | 4 | 8GB | 50GB | *************0 |
| Master01 | 4 | 8GB | 50GB | *************1 |
| Master02 | 4 | 8GB | 50GB | *************2 |
| Master03 | 4 | 8GB | 50GB | *************3 |
| Worker01 | 2 | 8GB | 50GB | ************** |
| Worker02 | 2 | 8GB | 50GB | ************** |
| Worker03 | 2 | 8GB | 50GB | ************** |

### MAC Addresses
| VM | MAC Address |
|----|-------------|
| Bootstrap | 52:54:00:a4:db:5f |
| Master01 | 52:54:00:8b:a1:17 |
| Master02 | 52:54:00:ea:8b:9d |
| Master03 | 52:54:00:f8:87:c7 |
| Worker01 | 52:54:00:31:4a:39 |
| Worker02 | 52:54:00:6a:37:32 |
| Worker03 | 52:54:00:95:d4:ed |

## Persiapan Environment

### Verifikasi CPU Virtualization Support
```bash
cat /proc/cpuinfo | egrep "vmx|svm"
```

### Install KVM Packages
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils virt-manager virt-builder

# RHEL/CentOS/Fedora
sudo dnf install -y qemu-kvm libvirt virt-install virt-manager virt-builder

# Start dan enable libvirt
sudo systemctl enable --now libvirtd
sudo usermod -aG libvirt $USER
```

## Setup KVM Infrastructure

### 1. Buat Virtual Network
```bash
# Buat file konfigurasi network
cat <<EOF > openshift4.xml
<network>
  <name>openshift4</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='openshift4' stp='on' delay='0'/>
  <domain name='openshift4'/>
  <ip address='*************' netmask='*************'>
  </ip>
</network>
EOF

# Define dan start network
sudo virsh net-define --file openshift4.xml
sudo virsh net-autostart openshift4
sudo virsh net-start openshift4

# Verifikasi
sudo virsh net-list
brctl show
```

## Buat Bastion VM

### 1. Download dan Buat VM Image
```bash
# Download Fedora 42 template
sudo virt-builder fedora-42 --format qcow2 \
  --size 20G -o /var/lib/libvirt/images/okd-bastion-server.qcow2 \
  --root-password password:rahasia

# Buat VM
sudo virt-install \
  --name okd-bastion-server \
  --ram 4096 \
  --vcpus 2 \
  --disk path=/var/lib/libvirt/images/okd-bastion-server.qcow2 \
  --os-variant rhel9.0 \
  --network bridge=openshift4,model=virtio \
  --graphics none \
  --serial pty \
  --console pty \
  --boot hd \
  --import
```

### 2. Konfigurasi Network di Bastion VM
```bash
# Login ke VM (password: rahasia)
sudo virsh console okd-bastion-server

# Di dalam VM, setup network
nmcli con add type ethernet con-name enp1s0 ifname enp1s0 \
  connection.autoconnect yes ipv4.method manual \
  ipv4.address ***************/24 ipv4.gateway ************* \
  ipv4.dns *******

# Test koneksi
ping -c 2 *******

# Update sistem
sudo dnf -y upgrade
sudo dnf -y install git vim wget curl bash-completion tree tar libselinux-python3 firewalld

# Reboot
sudo reboot

# Enable autostart
sudo virsh autostart okd-bastion-server
```

## Install Ansible dan Konfigurasi

### 1. Install Ansible di Bastion VM
```bash
# Di Bastion VM
sudo dnf -y install git ansible vim wget curl bash-completion tree tar libselinux-python3

# Clone repository
cd ~/
git clone https://github.com/jmutai/ocp4_ansible.git
cd ~/ocp4_ansible
```

### 2. Konfigurasi Ansible
```bash
# Edit ansible.cfg
cat <<EOF > ansible.cfg
[defaults]
inventory = inventory
command_warnings = False
host_key_checking = False
deprecation_warnings = False
retry_files = false

[privilege_escalation]
become = true
become_method = sudo
become_user = root
become_ask_pass = false
EOF

# Setup inventory
cat <<EOF > inventory
[vms_host]
localhost ansible_connection=local
EOF
```

### 3. Update Variables untuk OKD
```bash
# Edit vars/main.yml
cat <<EOF > vars/main.yml
---
ppc64le: false
uefi: false
disk: vda
helper:
  name: "bastion"
  ipaddr: "***************"
  networkifacename: "enp1s0"
dns:
  domain: "febryan.web.id.example"
  clusterid: "okd4"
  forwarder1: "*******"
  forwarder2: "*******"
  lb_ipaddr: "{{ helper.ipaddr }}"
dhcp:
  router: "*************"
  bcast: "***************"
  netmask: "*************"
  poolstart: "*************0"
  poolend: "**************"
  ipid: "*************"
  netmaskid: "*************"
  ntp: "time.google.com"
  dns: ""
bootstrap:
  name: "bootstrap"
  ipaddr: "*************0"
  macaddr: "52:54:00:a4:db:5f"
masters:
  - name: "master01"
    ipaddr: "*************1"
    macaddr: "52:54:00:8b:a1:17"
  - name: "master02"
    ipaddr: "*************2"
    macaddr: "52:54:00:ea:8b:9d"
  - name: "master03"
    ipaddr: "*************3"
    macaddr: "52:54:00:f8:87:c7"
workers:
  - name: "worker01"
    ipaddr: "**************"
    macaddr: "52:54:00:31:4a:39"
  - name: "worker02"
    ipaddr: "**************"
    macaddr: "52:54:00:6a:37:32"
  - name: "worker03"
    ipaddr: "**************"
    macaddr: "52:54:00:95:d4:ed"
EOF
```

## Setup DHCP Server

### 1. Install dan Konfigurasi DHCP
```bash
# Install DHCP server
sudo dnf -y install dhcp-server
sudo systemctl enable dhcpd

# Backup konfigurasi default
sudo mv /etc/dhcp/dhcpd.conf /etc/dhcp/dhcpd.conf.bak

# Jalankan konfigurasi dengan Ansible
ansible-playbook tasks/configure_dhcpd.yml

# Verifikasi
systemctl status dhcpd
cat /etc/dhcp/dhcpd.conf
```

## Setup DNS Server

### 1. Install dan Konfigurasi DNS
```bash
# Install BIND
sudo dnf -y install bind bind-utils
sudo systemctl enable named

# Install DNS serial script
sudo tee /usr/local/bin/set-dns-serial.sh<<EOF
#!/bin/bash
dnsserialfile=/usr/local/src/dnsserial-DO_NOT_DELETE_BEFORE_ASKING_CHRISTIAN.txt
zonefile=/var/named/zonefile.db
if [ -f zonefile ] ; then
    echo \$[ \$(grep serial \${zonefile} | tr -d "\t"" ""\n" | cut -d';' -f 1) + 1 ] | tee \${dnsserialfile}
else
    if [ ! -f \${dnsserialfile} ] || [ ! -s \${dnsserialfile} ]; then
        echo \$(date +%Y%m%d00) | tee \${dnsserialfile}
    else
        echo \$[ \$(< \${dnsserialfile}) + 1 ] | tee \${dnsserialfile}
    fi
fi
EOF

sudo chmod a+x /usr/local/bin/set-dns-serial.sh

# Konfigurasi DNS dengan Ansible
ansible-playbook tasks/configure_bind_dns.yml

# Test DNS
dig @127.0.0.1 -t srv _etcd-server-ssl._tcp.okd4.febryan.web.id.example

# Update network untuk menggunakan DNS lokal
nmcli connection modify enp1s0 ipv4.dns "***************"
nmcli connection reload
nmcli connection up enp1s0

# Test resolution
host bootstrap.okd4.febryan.web.id.example

# Open firewall
sudo firewall-cmd --add-service={dhcp,tftp,http,https,dns} --permanent
sudo firewall-cmd --reload
```

## Setup TFTP/PXE Server

### 1. Install TFTP Server
```bash
# Install packages
sudo dnf -y install tftp-server syslinux
sudo firewall-cmd --add-service=tftp --permanent
sudo firewall-cmd --reload

# Buat systemd service untuk TFTP
sudo tee /etc/systemd/system/helper-tftp.service<<EOF
[Unit]
Description=Starts TFTP on boot because of reasons
After=network.target

[Service]
Type=simple
ExecStart=/usr/local/bin/start-tftp.sh
TimeoutStartSec=0
Restart=always
RestartSec=30

[Install]
WantedBy=default.target
EOF

# Buat helper script
sudo tee /usr/local/bin/start-tftp.sh<<EOF
#!/bin/bash
/usr/bin/systemctl start tftp > /dev/null 2>&1
EOF

sudo chmod a+x /usr/local/bin/start-tftp.sh
sudo systemctl daemon-reload
sudo systemctl enable --now tftp helper-tftp

# Setup TFTP directories
sudo mkdir -p /var/lib/tftpboot/pxelinux.cfg
sudo cp -rvf /usr/share/syslinux/* /var/lib/tftpboot
sudo mkdir -p /var/lib/tftpboot/fcos
```

### 2. Download Fedora CoreOS Images
```bash
# Buat direktori untuk download
mkdir -p ~/fcos-images
cd ~/fcos-images

# Download FCOS 42 images (terbaru)
wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-live-kernel.x86_64
wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-live-initramfs.x86_64.img
wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-live-rootfs.x86_64.img

# Move ke lokasi TFTP
sudo mv fedora-coreos-42.20250705.3.0-live-kernel.x86_64 /var/lib/tftpboot/fcos/kernel
sudo mv fedora-coreos-42.20250705.3.0-live-initramfs.x86_64.img /var/lib/tftpboot/fcos/initramfs.img

# Setup web server untuk rootfs
sudo dnf -y install httpd
sudo mkdir -p /var/www/html/fcos
sudo mv fedora-coreos-42.20250705.3.0-live-rootfs.x86_64.img /var/www/html/fcos/rootfs.img

# Set permissions
sudo chmod 644 /var/lib/tftpboot/fcos/*
sudo chmod 644 /var/www/html/fcos/*
sudo restorecon -RFv /var/lib/tftpboot/fcos
sudo restorecon -RFv /var/www/html/fcos

# Konfigurasi httpd untuk port 8080
sudo sed -i 's/Listen 80/Listen 8080/' /etc/httpd/conf/httpd.conf
sudo rm -f /etc/httpd/conf.d/welcome.conf
sudo systemctl enable --now httpd
sudo firewall-cmd --add-port=8080/tcp --permanent
sudo firewall-cmd --reload
```

### 3. Update PXE Templates untuk FCOS
```bash
# Update template bootstrap
cat <<EOF > templates/pxe-bootstrap.j2
{% if bootstrap.ipaddr is defined and bootstrap.networkifacename is defined %}
  {% set ipconfig = bootstrap.ipaddr + "::" + dhcp.router + ":" + dhcp.netmask + ":" + bootstrap.name + ":" + bootstrap.networkifacename + ":none" %}
  {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder1 %}
  {% if dns.forwarder2 is defined %}
    {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder2 %}
  {% endif %}
{% else %}
  {% set ipconfig = "dhcp" %}
{% endif %}

default menu.c32
 prompt 1
 timeout 9
 ONTIMEOUT 1
 menu title ######## PXE Boot Menu - OKD Bootstrap ########
 label 1
 menu label ^1) Install OKD Bootstrap Node (FCOS)
 menu default
 kernel fcos/kernel
 append initrd=fcos/initramfs.img nomodeset rd.neednet=1 console=tty0 console=ttyS0 ip={{ ipconfig }} coreos.inst=yes coreos.inst.install_dev={{ disk }} coreos.live.rootfs_url=http://{{ helper.ipaddr }}:8080/fcos/rootfs.img coreos.inst.ignition_url=http://{{ helper.ipaddr }}:8080/ignition/bootstrap.ign
EOF

# Update template master
cat <<EOF > templates/pxe-master.j2
{% if item.ipaddr is defined and item.networkifacename is defined %}
  {% set ipconfig = item.ipaddr + "::" + dhcp.router + ":" + dhcp.netmask + ":" + item.name + ":" + item.networkifacename + ":none" %}
  {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder1 %}
  {% if dns.forwarder2 is defined %}
    {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder2 %}
  {% endif %}
{% else %}
  {% set ipconfig = "dhcp" %}
{% endif %}

default menu.c32
 prompt 1
 timeout 9
 ONTIMEOUT 1
 menu title ######## PXE Boot Menu - OKD Master ########
 label 1
 menu label ^1) Install OKD Master Node (FCOS)
 menu default
 kernel fcos/kernel
 append initrd=fcos/initramfs.img nomodeset rd.neednet=1 console=tty0 console=ttyS0 ip={{ ipconfig }} coreos.inst=yes coreos.inst.install_dev={{ disk }} coreos.live.rootfs_url=http://{{ helper.ipaddr }}:8080/fcos/rootfs.img coreos.inst.ignition_url=http://{{ helper.ipaddr }}:8080/ignition/master.ign
EOF

# Update template worker
cat <<EOF > templates/pxe-worker.j2
{% if item.ipaddr is defined and item.networkifacename is defined %}
  {% set ipconfig = item.ipaddr + "::" + dhcp.router + ":" + dhcp.netmask + ":" + item.name + ":" + item.networkifacename + ":none" %}
  {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder1 %}
  {% if dns.forwarder2 is defined %}
    {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder2 %}
  {% endif %}
{% else %}
  {% set ipconfig = "dhcp" %}
{% endif %}

default menu.c32
 prompt 1
 timeout 9
 ONTIMEOUT 1
 menu title ######## PXE Boot Menu - OKD Worker ########
 label 1
 menu label ^1) Install OKD Worker Node (FCOS)
 menu default
 kernel fcos/kernel
 append initrd=fcos/initramfs.img nomodeset rd.neednet=1 console=tty0 console=ttyS0 ip={{ ipconfig }} coreos.inst=yes coreos.inst.install_dev={{ disk }} coreos.live.rootfs_url=http://{{ helper.ipaddr }}:8080/fcos/rootfs.img coreos.inst.ignition_url=http://{{ helper.ipaddr }}:8080/ignition/worker.ign
EOF

# Generate PXE files
ansible-playbook tasks/configure_tftp_pxe.yml
```

## Setup HAProxy Load Balancer

### 1. Install dan Konfigurasi HAProxy
```bash
# Install HAProxy
sudo dnf install -y haproxy
sudo setsebool -P haproxy_connect_any 1
sudo mv /etc/haproxy/haproxy.cfg /etc/haproxy/haproxy.cfg.default

# Konfigurasi dengan Ansible
ansible-playbook tasks/configure_haproxy_lb.yml

# Setup SELinux ports
sudo dnf install -y policycoreutils-python-utils
sudo semanage port -a 6443 -t http_port_t -p tcp
sudo semanage port -a 22623 -t http_port_t -p tcp
sudo semanage port -a 32700 -t http_port_t -p tcp

# Open firewall
sudo firewall-cmd --add-service={http,https} --permanent
sudo firewall-cmd --add-port={6443,22623}/tcp --permanent
sudo firewall-cmd --reload

# Start HAProxy
sudo systemctl enable --now haproxy
```

## Download OKD Installer dan CLI

### 1. Download OKD 4.19 Binaries
```bash
cd ~/

# Download OKD Client (terbaru)
wget https://github.com/okd-project/okd/releases/download/4.19.0-okd-scos.10/openshift-client-linux-4.19.0-okd-scos.10.tar.gz
tar xvf openshift-client-linux-4.19.0-okd-scos.10.tar.gz
sudo mv oc kubectl /usr/local/bin

# Download OKD Installer (terbaru)
wget https://github.com/okd-project/okd/releases/download/4.19.0-okd-scos.10/openshift-install-linux-4.19.0-okd-scos.10.tar.gz
tar xvf openshift-install-linux-4.19.0-okd-scos.10.tar.gz
sudo mv openshift-install /usr/local/bin

# Cleanup
rm -f *.tar.gz README.md LICENSE

# Verifikasi
openshift-install version
oc version --client
```

### 2. Buat SSH Key
```bash
# Generate SSH key untuk akses ke cluster nodes
ssh-keygen -t rsa -N "" -f ~/.ssh/id_rsa
```

## Generate Ignition Files

### 1. Buat Install Config (Tanpa Pull Secret)
```bash
# Buat direktori untuk OKD
mkdir -p ~/okd4
cd ~/

# Install config tanpa pull secret (untuk OKD)
cat <<EOF > install-config-okd-no-pull-secret.yaml
apiVersion: v1
baseDomain: febryan.web.id.example
compute:
- hyperthreading: Enabled
  name: worker
  replicas: 0
controlPlane:
  hyperthreading: Enabled
  name: master
  replicas: 3
metadata:
  name: okd4
networking:
  clusterNetworks:
  - cidr: **********/14
    hostPrefix: 23
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
fips: false
sshKey: '$(< ~/.ssh/id_rsa.pub)'
EOF
```

### 2. Buat Install Config (Dengan Fake Pull Secret)
```bash
# Install config dengan fake pull secret (jika diperlukan)
cat <<EOF > install-config-okd-fake-pull-secret.yaml
apiVersion: v1
baseDomain: febryan.web.id.example
compute:
- hyperthreading: Enabled
  name: worker
  replicas: 0
controlPlane:
  hyperthreading: Enabled
  name: master
  replicas: 3
metadata:
  name: okd4
networking:
  clusterNetworks:
  - cidr: **********/14
    hostPrefix: 23
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
fips: false
pullSecret: '{"auths":{"fake":{"auth":"************"}}}'
sshKey: '$(< ~/.ssh/id_rsa.pub)'
EOF
```

### 3. Generate Ignition Files
```bash
# Gunakan config tanpa pull secret (recommended untuk OKD)
cp install-config-okd-no-pull-secret.yaml okd4/install-config.yaml
cd okd4

# Generate manifests
openshift-install create manifests

# Disable scheduling pada master nodes
sed -i 's/true/false/' manifests/cluster-scheduler-02-config.yml

# Generate ignition files
openshift-install create ignition-configs

# Copy ignition files ke web server
sudo mkdir -p /var/www/html/ignition
sudo cp -v *.ign /var/www/html/ignition/
sudo chmod 644 /var/www/html/ignition/*.ign
sudo restorecon -RFv /var/www/html/

# Verifikasi files
ls -la /var/www/html/ignition/
curl -I http://***************:8080/ignition/bootstrap.ign
```

## Buat Cluster VMs

### 1. Restart Semua Services
```bash
# Pastikan semua services berjalan
sudo systemctl enable --now haproxy dhcpd httpd tftp named
sudo systemctl restart haproxy dhcpd httpd tftp named
sudo systemctl status haproxy dhcpd httpd tftp named
```

### 2. Buat Bootstrap VM
```bash
# Bootstrap VM
sudo virt-install -n bootstrap.okd4.febryan.web.id.example \
  --description "Bootstrap Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=4 \
  --os-variant=rhel9.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:a4:db:5f

# Start bootstrap VM
sudo virsh start bootstrap.okd4.febryan.web.id.example
```

### 3. Buat Master VMs
```bash
# Master01 VM
sudo virt-install -n master01.okd4.febryan.web.id.example \
  --description "Master01 Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=4 \
  --os-variant=rhel9.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:8b:a1:17

# Master02 VM
sudo virt-install -n master02.okd4.febryan.web.id.example \
  --description "Master02 Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=4 \
  --os-variant=rhel9.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:ea:8b:9d

# Master03 VM
sudo virt-install -n master03.okd4.febryan.web.id.example \
  --description "Master03 Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=4 \
  --os-variant=rhel9.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:f8:87:c7
```

### 4. Buat Worker VMs
```bash
# Worker01 VM
sudo virt-install -n worker01.okd4.febryan.web.id.example \
  --description "Worker01 Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=2 \
  --os-variant=rhel9.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:31:4a:39

# Worker02 VM
sudo virt-install -n worker02.okd4.febryan.web.id.example \
  --description "Worker02 Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=2 \
  --os-variant=rhel9.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:6a:37:32

# Worker03 VM
sudo virt-install -n worker03.okd4.febryan.web.id.example \
  --description "Worker03 Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=2 \
  --os-variant=rhel9.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:95:d4:ed
```

## Install OKD Cluster

### 1. Monitor Bootstrap Process
```bash
# Monitor bootstrap completion
cd ~/okd4
openshift-install --dir=. wait-for bootstrap-complete --log-level=info

# Jika bootstrap berhasil, start master VMs
sudo virsh start master01.okd4.febryan.web.id.example
sudo virsh start master02.okd4.febryan.web.id.example
sudo virsh start master03.okd4.febryan.web.id.example

# Monitor logs (di terminal terpisah)
journalctl -f -u tftp
journalctl -f -u dhcpd
```

### 2. Approve Certificate Signing Requests (CSRs)
```bash
# Export kubeconfig
export KUBECONFIG=~/okd4/auth/kubeconfig

# Check pending CSRs
oc get csr

# Approve pending CSRs untuk master nodes
oc get csr -o go-template='{{range .items}}{{if not .status}}{{.metadata.name}}{{"\n"}}{{end}}{{end}}' | xargs oc adm certificate approve

# Start worker VMs setelah masters ready
sudo virsh start worker01.okd4.febryan.web.id.example
sudo virsh start worker02.okd4.febryan.web.id.example
sudo virsh start worker03.okd4.febryan.web.id.example

# Approve CSRs untuk worker nodes
oc get csr -o go-template='{{range .items}}{{if not .status}}{{.metadata.name}}{{"\n"}}{{end}}{{end}}' | xargs oc adm certificate approve
```

### 3. Complete Installation
```bash
# Wait for installation completion
openshift-install --dir=. wait-for install-complete --log-level=info

# Check cluster status
oc get nodes
oc get co  # cluster operators
oc get pods --all-namespaces

# Get cluster info
oc cluster-info
oc whoami --show-console
```

### 4. Access Web Console
```bash
# Get console URL dan admin password
echo "Console URL: $(oc whoami --show-console)"
echo "Admin Password: $(cat ~/okd4/auth/kubeadmin-password)"

# Login via CLI
oc login -u kubeadmin -p $(cat ~/okd4/auth/kubeadmin-password)
```

## Post-Installation Tasks

### 1. Configure Image Registry
```bash
# Configure image registry untuk bare metal
oc patch configs.imageregistry.operator.openshift.io cluster --type merge --patch '{"spec":{"storage":{"emptyDir":{}}}}'
oc patch configs.imageregistry.operator.openshift.io cluster --type merge --patch '{"spec":{"managementState":"Managed"}}'
```

### 2. Create Additional Users (Optional)
```bash
# Create htpasswd identity provider
htpasswd -c -B -b users.htpasswd admin admin123
htpasswd -b users.htpasswd developer dev123

# Create secret
oc create secret generic htpass-secret --from-file=htpasswd=users.htpasswd -n openshift-config

# Configure OAuth
cat <<EOF | oc apply -f -
apiVersion: config.openshift.io/v1
kind: OAuth
metadata:
  name: cluster
spec:
  identityProviders:
  - name: my_htpasswd_provider
    mappingMethod: claim
    type: HTPasswd
    htpasswd:
      fileData:
        name: htpass-secret
EOF

# Grant cluster-admin to admin user
oc adm policy add-cluster-role-to-user cluster-admin admin
```

## Troubleshooting

### Common Issues dan Solutions

1. **Bootstrap VM tidak boot dari PXE**
   ```bash
   # Check DHCP logs
   journalctl -f -u dhcpd

   # Check TFTP logs
   journalctl -f -u tftp

   # Verify PXE files
   ls -la /var/lib/tftpboot/pxelinux.cfg/
   ```

2. **DNS Resolution Issues**
   ```bash
   # Test DNS
   dig @*************** bootstrap.okd4.febryan.web.id.example
   nslookup api.okd4.febryan.web.id.example ***************
   ```

3. **Ignition Files tidak accessible**
   ```bash
   # Check httpd status
   systemctl status httpd

   # Test access
   curl -I http://***************:8080/ignition/bootstrap.ign
   curl -I http://***************:8080/fcos/rootfs.img
   ```

4. **HAProxy Issues**
   ```bash
   # Check HAProxy status
   systemctl status haproxy

   # Test load balancer
   curl -k https://api.okd4.febryan.web.id.example:6443/version
   ```

### Useful Commands

```bash
# Monitor cluster installation
watch -n 5 'oc get nodes; echo ""; oc get co'

# Check cluster version
oc get clusterversion

# Get cluster operators status
oc get co --sort-by='.metadata.name'

# Check events
oc get events --sort-by='.lastTimestamp'

# Get pod logs
oc logs -n openshift-kube-apiserver -l app=kube-apiserver

# Restart failed pods
oc delete pod --field-selector=status.phase=Failed --all-namespaces
```

## Kesimpulan

Tutorial ini memberikan panduan lengkap untuk menginstall OKD 4.19 dengan Fedora CoreOS di environment KVM. Cluster yang dihasilkan akan memiliki:

- 3 Master nodes (Control Plane)
- 3 Worker nodes (Compute)
- Load balancer (HAProxy)
- DNS server (BIND)
- DHCP server
- PXE/TFTP server

Setelah instalasi selesai, Anda akan memiliki cluster OKD yang fully functional dan siap untuk deploy aplikasi.

## Referensi

- [OKD Documentation](https://docs.okd.io/)
- [Fedora CoreOS Documentation](https://docs.fedoraproject.org/en-US/fedora-coreos/)
- [OpenShift Container Platform Documentation](https://docs.openshift.com/)
- [KVM/QEMU Documentation](https://www.qemu.org/documentation/)
