# OKD 4.19 Installation dengan Fedora CoreOS - Complete Guide

Repository ini berisi tutorial lengkap dan automation scripts untuk menginstall OKD 4.19 dengan Fedora CoreOS di environment KVM.

## 📁 File Structure

```
├── tutorial-okd-4.19-lengkap.md          # Tutorial lengkap format Markdown
├── tutorial-okd-4.19-lengkap.html        # Tutorial lengkap format HTML (bisa dibuka di browser)
├── tutorial-ocp-ganti-ke-okd-4.19.md     # Tutorial adaptasi dari OpenShift ke OKD
├── ansible-okd-automation.yml            # Ansible playbook untuk setup infrastructure
├── ansible-bastion-setup.yml             # Ansible playbook untuk setup services di bastion
├── vars/main.yml                          # Variables untuk OKD (sudah diupdate)
├── templates/pxe-*.j2                     # PXE templates untuk FCOS (sudah diupdate)
├── tasks/configure_tftp_pxe.yml           # Task untuk konfigurasi TFTP (sudah diupdate)
└── README-OKD-Installation.md             # File ini
```

## 🚀 Quick Start

### Opsi 1: Manual Installation (Mengikuti Tutorial)

1. **Baca Tutorial Lengkap:**
   - **Format Markdown:** `tutorial-okd-4.19-lengkap.md`
   - **Format HTML:** `tutorial-okd-4.19-lengkap.html` (buka di browser untuk pengalaman yang lebih baik)

2. **Ikuti langkah demi langkah** dari persiapan environment sampai instalasi cluster

### Opsi 2: Semi-Automated Installation

1. **Setup Infrastructure dengan Ansible:**
   ```bash
   # Di host KVM
   ansible-playbook ansible-okd-automation.yml
   ```

2. **Setup Services di Bastion VM:**
   ```bash
   # Di dalam Bastion VM setelah network dikonfigurasi
   ansible-playbook ansible-bastion-setup.yml
   ```

3. **Lanjutkan dengan manual steps** untuk generate ignition files dan start cluster VMs

## 📋 Prerequisites

### Hardware Requirements
- **Host Server:** Minimal 32GB RAM, 8 CPU cores, 500GB storage
- **Network:** Akses internet untuk download images dan packages

### Software Requirements
- **Host OS:** Linux dengan KVM support (Ubuntu 20.04+, RHEL 8+, Fedora 35+)
- **Virtualization:** KVM/QEMU dengan libvirt
- **Ansible:** Version 2.9+ (untuk automation)

## 🔧 Configuration Details

### Network Configuration
- **Network Range:** *************/24
- **Gateway:** *************
- **Bastion IP:** ***************
- **Domain:** febryan.web.id.example
- **Cluster Name:** okd4

### VM Specifications
| VM Type | vCPU | RAM | Storage | IP Address |
|---------|------|-----|---------|------------|
| Bastion | 2 | 4GB | 20GB | *************** |
| Bootstrap | 4 | 8GB | 50GB | *************0 |
| Master01-03 | 4 | 8GB | 50GB | *************1-13 |
| Worker01-03 | 2 | 8GB | 50GB | **************-23 |

## 🎯 Key Features

### ✅ Updated untuk OKD 4.19
- Menggunakan **Fedora CoreOS 42** (terbaru)
- **OKD 4.19.0-okd-scos.10** installer dan client
- **Tanpa Red Hat pull secret** (tidak diperlukan untuk OKD)
- Support untuk **fake pull secret** jika diperlukan

### ✅ Complete Automation
- **Infrastructure setup** dengan Ansible
- **Services configuration** otomatis
- **PXE templates** sudah disesuaikan untuk FCOS
- **Firewall rules** dan **SELinux** dikonfigurasi otomatis

### ✅ Production Ready
- **Load balancer** dengan HAProxy
- **DNS server** dengan BIND
- **DHCP server** dengan reservasi IP
- **PXE/TFTP server** untuk network boot
- **HTTP server** untuk serving images dan ignition files

## 📖 Detailed Instructions

### 1. Infrastructure Setup (Host Level)

```bash
# Clone repository
git clone <repository-url>
cd ocp4_ansible

# Review dan sesuaikan variables jika perlu
vim ansible-okd-automation.yml

# Jalankan infrastructure setup
ansible-playbook ansible-okd-automation.yml

# Connect ke bastion VM
sudo virsh console okd-bastion-server
# Login: root / rahasia
```

### 2. Network Configuration (Bastion VM)

```bash
# Di dalam Bastion VM
nmcli con add type ethernet con-name enp1s0 ifname enp1s0 \
  connection.autoconnect yes ipv4.method manual \
  ipv4.address ***************/24 ipv4.gateway ************* \
  ipv4.dns *******

# Test koneksi
ping -c 2 *******
```

### 3. Services Setup (Bastion VM)

```bash
# Update sistem
dnf -y upgrade && reboot

# Download dan jalankan bastion setup
wget <ansible-bastion-setup.yml-url>
ansible-playbook ansible-bastion-setup.yml
```

### 4. Generate Ignition Files

```bash
# Generate SSH key
ssh-keygen -t rsa -N "" -f ~/.ssh/id_rsa

# Buat install config (tanpa pull secret)
mkdir -p ~/okd4
cat <<EOF > ~/install-config-okd.yaml
apiVersion: v1
baseDomain: febryan.web.id.example
compute:
- hyperthreading: Enabled
  name: worker
  replicas: 0
controlPlane:
  hyperthreading: Enabled
  name: master
  replicas: 3
metadata:
  name: okd4
networking:
  clusterNetworks:
  - cidr: **********/14
    hostPrefix: 23
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
fips: false
sshKey: '$(< ~/.ssh/id_rsa.pub)'
EOF

# Generate ignition files
cp install-config-okd.yaml okd4/install-config.yaml
cd okd4
openshift-install create manifests
sed -i 's/true/false/' manifests/cluster-scheduler-02-config.yml
openshift-install create ignition-configs

# Copy ke web server
sudo cp -v *.ign /var/www/html/ignition/
sudo chmod 644 /var/www/html/ignition/*.ign
sudo restorecon -RFv /var/www/html/
```

### 5. Start Cluster Installation

```bash
# Start bootstrap VM (sudah dibuat oleh automation)
sudo virsh start bootstrap.okd4.febryan.web.id.example

# Monitor bootstrap process
cd ~/okd4
openshift-install --dir=. wait-for bootstrap-complete --log-level=info

# Start master VMs setelah bootstrap selesai
sudo virsh start master01.okd4.febryan.web.id.example
sudo virsh start master02.okd4.febryan.web.id.example
sudo virsh start master03.okd4.febryan.web.id.example

# Approve CSRs dan complete installation
export KUBECONFIG=~/okd4/auth/kubeconfig
oc get csr -o go-template='{{range .items}}{{if not .status}}{{.metadata.name}}{{"\n"}}{{end}}{{end}}' | xargs oc adm certificate approve

# Wait for installation completion
openshift-install --dir=. wait-for install-complete --log-level=info
```

## 🔍 Troubleshooting

### Common Issues

1. **VM tidak boot dari PXE:**
   ```bash
   journalctl -f -u dhcpd
   journalctl -f -u tftp
   ls -la /var/lib/tftpboot/pxelinux.cfg/
   ```

2. **DNS tidak resolve:**
   ```bash
   dig @*************** bootstrap.okd4.febryan.web.id.example
   systemctl status named
   ```

3. **Images tidak accessible:**
   ```bash
   curl -I http://***************:8080/fcos/rootfs.img
   curl -I http://***************:8080/ignition/bootstrap.ign
   systemctl status httpd
   ```

### Useful Commands

```bash
# Check all services
systemctl status dhcpd named tftp httpd haproxy

# Monitor cluster installation
watch -n 5 'oc get nodes; echo ""; oc get co'

# Check cluster version
oc get clusterversion

# Get console URL dan admin password
echo "Console URL: $(oc whoami --show-console)"
echo "Admin Password: $(cat ~/okd4/auth/kubeadmin-password)"
```

## 📚 Additional Resources

- [OKD Documentation](https://docs.okd.io/)
- [Fedora CoreOS Documentation](https://docs.fedoraproject.org/en-US/fedora-coreos/)
- [OpenShift Container Platform Documentation](https://docs.openshift.com/)

## 🤝 Contributing

Jika Anda menemukan issues atau ingin berkontribusi:

1. Fork repository ini
2. Buat feature branch
3. Commit changes
4. Push ke branch
5. Create Pull Request

## 📄 License

Tutorial dan scripts ini disediakan "as-is" untuk tujuan edukasi dan development. Gunakan dengan risiko sendiri untuk environment production.

## 🎉 Selamat!

Setelah mengikuti tutorial ini, Anda akan memiliki:
- ✅ OKD 4.19 cluster yang fully functional
- ✅ 3 Master nodes + 3 Worker nodes
- ✅ Complete infrastructure services
- ✅ Web console access
- ✅ CLI tools configured

Happy clustering! 🚀
