# Tutorial: Mengubah OpenShift 4.17 ke OKD 4.19 dengan Fedora CoreOS (FCOS)

## <PERSON><PERSON><PERSON>an

Berdasarkan progress Anda yang sudah sampai tahap 6 (konfigurasi TFTP), be<PERSON>ut adalah adaptasi untuk menggunakan OKD 4.19 dengan Fedora CoreOS (FCOS) instead of RHCOS.

## Perbedaan Utama OKD vs OpenShift

1. **Operating System**: OKD 4.19 menggunakan Fedora CoreOS (FCOS) bukan Red Hat CoreOS (RHCOS)
2. **Download Sources**: Mirror berbeda untuk FCOS images
3. **Installer**: Menggunakan OKD installer bukan OpenShift installer
4. **Pull Secret**: OKD tidak memerlukan Red Hat pull secret

## Langkah-langkah Adaptasi

### 1. Update Variables (vars/main.yml)

Sesuaikan dengan IP range 192.168.101.x yang sudah Anda gunakan:

```yaml
---
ppc64le: false
uefi: false
disk: vda
helper:
  name: "bastion"
  ipaddr: "***************"
  networkifacename: "enp1s0"
dns:
  domain: "febryan.web.id.example"
  clusterid: "okd4"  # Changed from ocp4 to okd4
  forwarder1: "*******"
  forwarder2: "*******"
  lb_ipaddr: "{{ helper.ipaddr }}"
dhcp:
  router: "*************"
  bcast: "***************"
  netmask: "*************"
  poolstart: "**************"
  poolend: "**************"
  ipid: "*************"
  netmaskid: "*************"
  ntp: "time.google.com"
  dns: ""
bootstrap:
  name: "bootstrap"
  ipaddr: "**************"
  macaddr: "52:54:00:a4:db:5f"
masters:
  - name: "master01"
    ipaddr: "*************1"
    macaddr: "52:54:00:8b:a1:17"
  - name: "master02"
    ipaddr: "*************2"
    macaddr: "52:54:00:ea:8b:9d"
  - name: "master03"
    ipaddr: "*************3"
    macaddr: "52:54:00:f8:87:c7"
workers:
  - name: "worker01"
    ipaddr: "**************"
    macaddr: "52:54:00:31:4a:39"
  - name: "worker02"
    ipaddr: "**************"
    macaddr: "52:54:00:6a:37:32"
  - name: "worker03"
    ipaddr: "**************"
    macaddr: "52:54:00:95:d4:ed"
```

### 2. Download FCOS Images (Ganti RHCOS)

Hapus file RHCOS yang sudah didownload dan ganti dengan FCOS:

```bash
# Hapus file RHCOS lama
sudo rm -rf /var/lib/tftpboot/rhcos/*
sudo rm -rf /var/www/html/rhcos/*

# Buat direktori untuk FCOS
sudo mkdir -p /var/lib/tftpboot/fcos
sudo mkdir -p /var/www/html/fcos

# Download FCOS images untuk OKD 4.19
cd ~/
mkdir fcos-images
cd fcos-images

# Download FCOS kernel
wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/40.20240906.3.0/x86_64/fedora-coreos-40.20240906.3.0-live-kernel-x86_64

# Download FCOS initramfs
wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/40.20240906.3.0/x86_64/fedora-coreos-40.20240906.3.0-live-initramfs.x86_64.img

# Download FCOS rootfs
wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/40.20240906.3.0/x86_64/fedora-coreos-40.20240906.3.0-live-rootfs.x86_64.img

# Move files ke lokasi yang benar
sudo mv fedora-coreos-40.20240906.3.0-live-kernel-x86_64 /var/lib/tftpboot/fcos/kernel
sudo mv fedora-coreos-40.20240906.3.0-live-initramfs.x86_64.img /var/lib/tftpboot/fcos/initramfs.img
sudo mv fedora-coreos-40.20240906.3.0-live-rootfs.x86_64.img /var/www/html/fcos/rootfs.img

# Set permissions
sudo chmod 644 /var/lib/tftpboot/fcos/*
sudo chmod 644 /var/www/html/fcos/*
sudo restorecon -RFv /var/lib/tftpboot/fcos
sudo restorecon -RFv /var/www/html/fcos
```

### 3. Update PXE Templates untuk FCOS

Perlu mengupdate template PXE untuk menggunakan path FCOS:

#### Update pxe-bootstrap.j2:
```jinja2
{% if bootstrap.ipaddr is defined and bootstrap.networkifacename is defined %}
  {% set ipconfig = bootstrap.ipaddr + "::" + dhcp.router + ":" + dhcp.netmask + ":" + bootstrap.name + ":" + bootstrap.networkifacename + ":none" %}
  {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder1 %}
  {% if dns.forwarder2 is defined %}
    {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder2 %}
  {% endif %}
{% else %}
  {% set ipconfig = "dhcp" %}
{% endif %}

default menu.c32
 prompt 1
 timeout 9
 ONTIMEOUT 1
 menu title ######## PXE Boot Menu - OKD Bootstrap ########
 label 1
 menu label ^1) Install OKD Bootstrap Node (FCOS)
 menu default
 kernel fcos/kernel
 append initrd=fcos/initramfs.img nomodeset rd.neednet=1 console=tty0 console=ttyS0 ip={{ ipconfig }} coreos.inst=yes coreos.inst.install_dev={{ disk }} coreos.live.rootfs_url=http://{{ helper.ipaddr }}:8080/fcos/rootfs.img coreos.inst.ignition_url=http://{{ helper.ipaddr }}:8080/ignition/bootstrap.ign
```

#### Update pxe-master.j2:
```jinja2
{% if item.ipaddr is defined and item.networkifacename is defined %}
  {% set ipconfig = item.ipaddr + "::" + dhcp.router + ":" + dhcp.netmask + ":" + item.name + ":" + item.networkifacename + ":none" %}
  {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder1 %}
  {% if dns.forwarder2 is defined %}
    {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder2 %}
  {% endif %}
{% else %}
  {% set ipconfig = "dhcp" %}
{% endif %}

default menu.c32
 prompt 1
 timeout 9
 ONTIMEOUT 1
 menu title ######## PXE Boot Menu - OKD Master ########
 label 1
 menu label ^1) Install OKD Master Node (FCOS)
 menu default
 kernel fcos/kernel
 append initrd=fcos/initramfs.img nomodeset rd.neednet=1 console=tty0 console=ttyS0 ip={{ ipconfig }} coreos.inst=yes coreos.inst.install_dev={{ disk }} coreos.live.rootfs_url=http://{{ helper.ipaddr }}:8080/fcos/rootfs.img coreos.inst.ignition_url=http://{{ helper.ipaddr }}:8080/ignition/master.ign
```

#### Update pxe-worker.j2:
```jinja2
{% if item.ipaddr is defined and item.networkifacename is defined %}
  {% set ipconfig = item.ipaddr + "::" + dhcp.router + ":" + dhcp.netmask + ":" + item.name + ":" + item.networkifacename + ":none" %}
  {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder1 %}
  {% if dns.forwarder2 is defined %}
    {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder2 %}
  {% endif %}
{% else %}
  {% set ipconfig = "dhcp" %}
{% endif %}

default menu.c32
 prompt 1
 timeout 9
 ONTIMEOUT 1
 menu title ######## PXE Boot Menu - OKD Worker ########
 label 1
 menu label ^1) Install OKD Worker Node (FCOS)
 menu default
 kernel fcos/kernel
 append initrd=fcos/initramfs.img nomodeset rd.neednet=1 console=tty0 console=ttyS0 ip={{ ipconfig }} coreos.inst=yes coreos.inst.install_dev={{ disk }} coreos.live.rootfs_url=http://{{ helper.ipaddr }}:8080/fcos/rootfs.img coreos.inst.ignition_url=http://{{ helper.ipaddr }}:8080/ignition/worker.ign
```

### 4. Download OKD Installer dan CLI

```bash
# Download OKD 4.19 installer dan client
cd ~/

# OKD Client
wget https://github.com/okd-project/okd/releases/download/4.19.0-0.okd-2024-09-27-114407/openshift-client-linux-4.19.0-0.okd-2024-09-27-114407.tar.gz
tar xvf openshift-client-linux-4.19.0-0.okd-2024-09-27-114407.tar.gz
sudo mv oc kubectl /usr/local/bin
rm -f README.md LICENSE openshift-client-linux-4.19.0-0.okd-2024-09-27-114407.tar.gz

# OKD Installer
wget https://github.com/okd-project/okd/releases/download/4.19.0-0.okd-2024-09-27-114407/openshift-install-linux-4.19.0-0.okd-2024-09-27-114407.tar.gz
tar xvf openshift-install-linux-4.19.0-0.okd-2024-09-27-114407.tar.gz
sudo mv openshift-install /usr/local/bin
rm -f README.md LICENSE openshift-install-linux-4.19.0-0.okd-2024-09-27-114407.tar.gz
```

### 5. Buat SSH Key dan Install Config untuk OKD

```bash
# Generate SSH key jika belum ada
ssh-keygen -t rsa -N "" -f ~/.ssh/id_rsa

# Buat direktori untuk OKD
mkdir -p ~/okd4
cd ~/

# Buat install-config untuk OKD (tidak perlu pull secret)
cat <<EOF > install-config-okd.yaml
apiVersion: v1
baseDomain: febryan.web.id.example
compute:
- hyperthreading: Enabled
  name: worker
  replicas: 0
controlPlane:
  hyperthreading: Enabled
  name: master
  replicas: 3
metadata:
  name: okd4
networking:
  clusterNetworks:
  - cidr: **********/14
    hostPrefix: 23
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
fips: false
sshKey: '$(< ~/.ssh/id_rsa.pub)'
EOF
```

### 6. Generate Ignition Files untuk OKD

```bash
# Copy install config ke direktori okd4
cp install-config-okd.yaml okd4/install-config.yaml
cd okd4

# Generate manifests
openshift-install create manifests

# Disable scheduling pada master nodes
sed -i 's/true/false/' manifests/cluster-scheduler-02-config.yml

# Generate ignition files
openshift-install create ignition-configs

# Copy ignition files ke web server
sudo mkdir -p /var/www/html/ignition
sudo cp -v *.ign /var/www/html/ignition/
sudo chmod 644 /var/www/html/ignition/*.ign
sudo restorecon -RFv /var/www/html/
```

### 7. Regenerate PXE Files dengan Template Baru

```bash
cd ~/ocp4_ansible

# Jalankan ulang konfigurasi TFTP dengan template yang sudah diupdate
ansible-playbook tasks/configure_tftp_pxe.yml
```

### 8. Verifikasi dan Start Services

```bash
# Pastikan semua services berjalan
sudo systemctl enable --now haproxy.service dhcpd httpd tftp named
sudo systemctl restart haproxy.service dhcpd httpd tftp named
sudo systemctl status haproxy.service dhcpd httpd tftp named

# Test akses ke FCOS images
curl -I http://***************:8080/fcos/rootfs.img
curl -I http://***************:8080/ignition/bootstrap.ign
```

### 9. Buat VMs untuk OKD

Sekarang Anda bisa membuat VMs dengan nama yang sesuai untuk OKD:

```bash
# Bootstrap VM
sudo virt-install -n bootstrap.okd4.febryan.web.id.example \
  --description "Bootstrap Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=4 \
  --os-type=Linux \
  --os-variant=rhel8.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:a4:db:5f
```

## Perbedaan Penting OKD vs OpenShift

1. **Tidak perlu Red Hat subscription atau pull secret**
2. **Menggunakan Fedora CoreOS bukan RHCOS**
3. **Download dari GitHub releases bukan Red Hat mirror**
4. **Domain cluster menggunakan okd4 bukan ocp4**

## Next Steps

Setelah semua konfigurasi selesai, Anda bisa:
1. Start bootstrap VM
2. Monitor bootstrap process: `openshift-install --dir=okd4 wait-for bootstrap-complete --log-level=info`
3. Start master VMs
4. Start worker VMs
5. Complete installation: `openshift-install --dir=okd4 wait-for install-complete --log-level=info`

## Langkah-langkah Eksekusi Berdasarkan Progress Anda

Karena Anda sudah sampai tahap 6, berikut langkah-langkah yang perlu dilakukan:

### 1. Update Variables dan Templates (Sudah dilakukan di file ini)
```bash
cd ~/ocp4_ansible
# File vars/main.yml, templates/pxe-*.j2 sudah diupdate untuk OKD + FCOS
```

### 2. Download dan Setup FCOS Images
```bash
# Hapus RHCOS lama dan setup FCOS
sudo rm -rf /var/lib/tftpboot/rhcos/*
sudo rm -rf /var/www/html/rhcos/*

# Buat direktori FCOS
sudo mkdir -p /var/lib/tftpboot/fcos
sudo mkdir -p /var/www/html/fcos

# Download FCOS images
cd ~/
mkdir -p fcos-images
cd fcos-images

# Download FCOS untuk OKD 4.19
wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/40.20240906.3.0/x86_64/fedora-coreos-40.20240906.3.0-live-kernel-x86_64
wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/40.20240906.3.0/x86_64/fedora-coreos-40.20240906.3.0-live-initramfs.x86_64.img
wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/40.20240906.3.0/x86_64/fedora-coreos-40.20240906.3.0-live-rootfs.x86_64.img

# Move ke lokasi yang benar
sudo mv fedora-coreos-40.20240906.3.0-live-kernel-x86_64 /var/lib/tftpboot/fcos/kernel
sudo mv fedora-coreos-40.20240906.3.0-live-initramfs.x86_64.img /var/lib/tftpboot/fcos/initramfs.img
sudo mv fedora-coreos-40.20240906.3.0-live-rootfs.x86_64.img /var/www/html/fcos/rootfs.img

# Set permissions
sudo chmod 644 /var/lib/tftpboot/fcos/*
sudo chmod 644 /var/www/html/fcos/*
sudo restorecon -RFv /var/lib/tftpboot/fcos
sudo restorecon -RFv /var/www/html/fcos
```

### 3. Download OKD Installer
```bash
cd ~/

# Download OKD 4.19 client dan installer
wget https://github.com/okd-project/okd/releases/download/4.19.0-0.okd-2024-09-27-114407/openshift-client-linux-4.19.0-0.okd-2024-09-27-114407.tar.gz
tar xvf openshift-client-linux-4.19.0-0.okd-2024-09-27-114407.tar.gz
sudo mv oc kubectl /usr/local/bin

wget https://github.com/okd-project/okd/releases/download/4.19.0-0.okd-2024-09-27-114407/openshift-install-linux-4.19.0-0.okd-2024-09-27-114407.tar.gz
tar xvf openshift-install-linux-4.19.0-0.okd-2024-09-27-114407.tar.gz
sudo mv openshift-install /usr/local/bin

# Cleanup
rm -f *.tar.gz README.md LICENSE
```

### 4. Generate Ignition Files untuk OKD
```bash
# Buat SSH key jika belum ada
ssh-keygen -t rsa -N "" -f ~/.ssh/id_rsa

# Buat install config untuk OKD
mkdir -p ~/okd4
cat <<EOF > ~/install-config-okd.yaml
apiVersion: v1
baseDomain: febryan.web.id.example
compute:
- hyperthreading: Enabled
  name: worker
  replicas: 0
controlPlane:
  hyperthreading: Enabled
  name: master
  replicas: 3
metadata:
  name: okd4
networking:
  clusterNetworks:
  - cidr: **********/14
    hostPrefix: 23
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
fips: false
sshKey: '$(< ~/.ssh/id_rsa.pub)'
EOF

# Generate ignition files
cp install-config-okd.yaml okd4/install-config.yaml
cd okd4
openshift-install create manifests
sed -i 's/true/false/' manifests/cluster-scheduler-02-config.yml
openshift-install create ignition-configs

# Copy ke web server
sudo mkdir -p /var/www/html/ignition
sudo cp -v *.ign /var/www/html/ignition/
sudo chmod 644 /var/www/html/ignition/*.ign
sudo restorecon -RFv /var/www/html/
```

### 5. Regenerate PXE Files dengan Template Baru
```bash
cd ~/ocp4_ansible
ansible-playbook tasks/configure_tftp_pxe.yml
```

### 6. Restart Services dan Verifikasi
```bash
# Restart semua services
sudo systemctl restart haproxy dhcpd httpd tftp named

# Verifikasi
curl -I http://***************:8080/fcos/rootfs.img
curl -I http://***************:8080/ignition/bootstrap.ign
ls -la /var/lib/tftpboot/pxelinux.cfg/
```

### 7. Test DNS untuk OKD
```bash
# Test DNS resolution untuk OKD
dig @127.0.0.1 -t srv _etcd-server-ssl._tcp.okd4.febryan.web.id.example
host bootstrap.okd4.febryan.web.id.example
```

### 8. Buat VMs untuk OKD
```bash
# Bootstrap VM
sudo virt-install -n bootstrap.okd4.febryan.web.id.example \
  --description "Bootstrap Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=4 \
  --os-type=Linux \
  --os-variant=rhel8.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:a4:db:5f

# Start bootstrap
sudo virsh start bootstrap.okd4.febryan.web.id.example

# Monitor bootstrap process
openshift-install --dir=~/okd4 wait-for bootstrap-complete --log-level=info
```

## Troubleshooting

- Pastikan FCOS images sudah terdownload dengan benar
- Cek firewall rules untuk port 8080
- Monitor logs: `journalctl -f -u tftp`, `journalctl -f -u dhcpd`
- Verifikasi DNS resolution untuk domain okd4.febryan.web.id.example
- Pastikan MAC addresses di PXE files sesuai dengan VM yang dibuat