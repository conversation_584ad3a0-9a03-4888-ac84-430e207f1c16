---
ppc64le: false
uefi: false
disk: vda                                  #disk where you are installing FCOS on the masters/workers
helper:
  name: "bastion"                          #hostname for your helper node
  ipaddr: "***************"                #current IP address of the helper
  networkifacename: "enp1s0"               #interface of the helper node,ACTUAL name of the interface, NOT the NetworkManager name, check with $ ip ad
dns:
  domain: "febryan.web.id.example"         #DNS server domain. Should match  baseDomain inside the install-config.yaml file.
  clusterid: "okd4"                        #needs to match what you will for metadata.name inside the install-config.yaml file
  forwarder1: "*******"                    #DNS forwarder
  forwarder2: "*******"                    #second DNS forwarder
  lb_ipaddr: "{{ helper.ipaddr }}"         #Load balancer IP, it is optional, the default value is helper.ipaddr
dhcp:
  router: "*************"                  #default gateway of the network assigned to the masters/workers
  bcast: "***************"                 #broadcast address for your network
  netmask: "*************"                 #netmask that gets assigned to your masters/workers
  poolstart: "*************0"              #First address in your dhcp address pool
  poolend: "**************"                #Last address in your dhcp address pool
  ipid: "*************"                    #ip network id for the range
  netmaskid: "*************"               #networkmask id for the range.
  ntp: "time.google.com"                   #ntp server address
  dns: ""                                  #domain name server, it is optional, the default value is set to helper.ipaddr
bootstrap:
  name: "bootstrap"                        #hostname (WITHOUT the fqdn) of the bootstrap node
  ipaddr: "*************0"                 #IP address that you want set for bootstrap node
  macaddr: "52:54:00:a4:db:5f"             #The mac address for dhcp reservation
masters:
  - name: "master01"                       #hostname (WITHOUT the fqdn) of the master node (x of 3)
    ipaddr: "*************1"               #The IP address (x of 3) that you want set
    macaddr: "52:54:00:8b:a1:17"           #The mac address for dhcp reservation
  - name: "master02"
    ipaddr: "*************2"
    macaddr: "52:54:00:ea:8b:9d"
  - name: "master03"
    ipaddr: "*************3"
    macaddr: "52:54:00:f8:87:c7"
workers:
  - name: "worker01"                       #hostname (WITHOUT the fqdn) of the worker node you want to set
    ipaddr: "**************"               #The IP address that you want set (1st node)
    macaddr: "52:54:00:31:4a:39"           #The mac address for dhcp reservation (1st node)
  - name: "worker02"
    ipaddr: "**************"
    macaddr: "52:54:00:6a:37:32"
  - name: "worker03"
    ipaddr: "**************"
    macaddr: "52:54:00:95:d4:ed"
