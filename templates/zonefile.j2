$TTL 1W
@	IN	SOA	ns1.{{ dns.clusterid }}.{{ dns.domain | lower }}.	root (
			{{ serialnumber }}	; serial
			3H		; refresh (3 hours)
			30M		; retry (30 minutes)
			2W		; expiry (2 weeks)
			1W )		; minimum (1 week)
	IN	NS	ns1.{{ dns.clusterid }}.{{ dns.domain | lower }}.
	IN	MX 10	smtp.{{ dns.clusterid }}.{{ dns.domain | lower }}.
;
; 
ns1	IN	A	{{ helper.ipaddr }}
smtp	IN	A	{{ helper.ipaddr }}
;
{{ helper.name | lower }}	IN	A	{{ helper.ipaddr }}
;
{%if high_availability is defined %}
; Create entry for the bastion hosts
{% for h in high_availability.helpernodes %}
{{ h.name | lower }}		IN	A	{{ h.ipaddr }}
{% endfor %}
{% endif %}
;
; The api points to the IP of your load balancer
{% if dns.lb_ipaddr is defined %}
api		    IN	A	{{ dns.lb_ipaddr }}
api-int		IN	A	{{ dns.lb_ipaddr }}
;
; The wildcard also points to the load balancer
*.apps		IN	A	{{ dns.lb_ipaddr }}
{% else %}
api			IN	A	{{ helper.ipaddr }}
api-int		IN	A	{{ helper.ipaddr }}
;
; The wildcard also points to the load balancer
*.apps		IN	A	{{ helper.ipaddr }}
{% endif %}
;
{% if bootstrap is defined %}
; Create entry for the bootstrap host
{{ bootstrap.name | lower }}	IN	A	{{ bootstrap.ipaddr }}
;
{% endif %}
; Create entries for the master hosts
{% for m in masters %}
{{ m.name | lower }}		IN	A	{{ m.ipaddr }}
{% endfor %}
;
; Create entries for the worker hosts
{% if workers is defined %}
{% for w in workers %}
{{ w.name | lower }}		IN	A	{{ w.ipaddr }}
{% endfor %}
{% endif %}
;
; The ETCd cluster lives on the masters...so point these to the IP of the masters
{% for m in masters %}
etcd-{{ loop.index0 }}	IN	A	{{ m.ipaddr }}
{% endfor %}
;
{% if other is defined %}
; Create entries for the other hosts
{% for o in other %}
{{ o.name }}		IN	A	{{ o.ipaddr }}
{% endfor %}
;
{% endif %}
; The SRV records are IMPORTANT....make sure you get these right...note the trailing dot at the end...
{% for m in masters %}
_etcd-server-ssl._tcp	IN	SRV	0 10 2380 etcd-{{ loop.index0 }}.{{ dns.clusterid }}.{{ dns.domain | lower }}.
{% endfor %}
;
;EOF
