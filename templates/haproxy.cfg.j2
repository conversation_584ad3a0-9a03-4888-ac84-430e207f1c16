#---------------------------------------------------------------------
# Example configuration for a possible web application.  See the
# full configuration options online.
#
#   http://haproxy.1wt.eu/download/1.4/doc/configuration.txt
#
#---------------------------------------------------------------------

#---------------------------------------------------------------------
# Global settings
#---------------------------------------------------------------------
global
    # to have these messages end up in /var/log/haproxy.log you will
    # need to:
    #
    # 1) configure syslog to accept network log events.  This is done
    #    by adding the '-r' option to the SYSLOGD_OPTIONS in
    #    /etc/sysconfig/syslog
    #
    # 2) configure local2 events to go to the /var/log/haproxy.log
    #   file. A line like the following can be added to
    #   /etc/sysconfig/syslog
    #
    #    local2.*                       /var/log/haproxy.log
    #
    log         127.0.0.1 local2

    chroot      /var/lib/haproxy
    pidfile     /var/run/haproxy.pid
    maxconn     4000
    user        haproxy
    group       haproxy
    daemon

    # turn on stats unix socket
    stats socket /var/lib/haproxy/stats

#---------------------------------------------------------------------
# common defaults that all the 'listen' and 'backend' sections will
# use if not designated in their block
#---------------------------------------------------------------------
defaults
    mode                    tcp
    log                     global
    option                  httplog
    option                  dontlognull
    option http-server-close
    option forwardfor       except 127.0.0.0/8
    option                  redispatch
    retries                 3
    timeout http-request    10s
    timeout queue           1m
    timeout connect         10s
    timeout client          4h
    timeout server          4h
    timeout http-keep-alive 10s
    timeout check           10s
    maxconn                 3000

#---------------------------------------------------------------------

listen stats
    bind :9000
    mode http
    stats enable
    stats uri /
    monitor-uri /healthz


frontend openshift-api-server
    bind *:6443
    default_backend openshift-api-server
    option tcplog

backend openshift-api-server
    balance source
{% if bootstrap is defined %}
    server {{ bootstrap.name | lower }} {{ bootstrap.ipaddr }}:6443 check
{% endif %}
{% for m in masters %}
    server {{ m.name | lower }} {{ m.ipaddr }}:6443 check
{% endfor %}
    
frontend machine-config-server
    bind *:22623
    default_backend machine-config-server
    option tcplog

backend machine-config-server
    balance source
{% if bootstrap is defined %}
    server {{ bootstrap.name | lower }} {{ bootstrap.ipaddr }}:22623 check
{% endif %}
{% for m in masters %}
    server {{ m.name | lower }} {{ m.ipaddr }}:22623 check
{% endfor %}
  
frontend ingress-http
    bind *:80
    default_backend ingress-http
    option tcplog

backend ingress-http
    balance source
{% if workers is defined %}
{% for w in workers %}
    server {{ w.name | lower }}-http-router{{ loop.index0 }} {{ w.ipaddr }}:80 check
{% endfor %}
{% else %}
{% for m in masters %}
    server {{ m.name | lower }}-http-router{{ loop.index0 }} {{ m.ipaddr }}:80 check
{% endfor %}
{% endif %}
   
frontend ingress-https
    bind *:443
    default_backend ingress-https
    option tcplog

backend ingress-https
    balance source
{% if workers is defined %}
{% for w in workers %}
    server {{ w.name | lower }}-https-router{{ loop.index0 }} {{ w.ipaddr }}:443 check
{% endfor %}
{% else %}
{% for m in masters %}
    server {{ m.name | lower }}-https-router{{ loop.index0 }} {{ m.ipaddr }}:443 check
{% endfor %}
{% endif %}

#---------------------------------------------------------------------
