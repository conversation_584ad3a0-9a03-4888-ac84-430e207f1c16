{% if bootstrap.ipaddr is defined and bootstrap.networkifacename is defined %}
  {% set ipconfig = bootstrap.ipaddr + "::" + dhcp.router + ":" + dhcp.netmask + ":" + bootstrap.name + ":" + bootstrap.networkifacename + ":none" %}
  {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder1 %}
  {% if dns.forwarder2 is defined %}
    {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder2 %}
  {% endif %}
{% else %}
  {% set ipconfig = "dhcp" %}
{% endif %}

default menu.c32
 prompt 1
 timeout 9
 ONTIMEOUT 1
 menu title ######## PXE Boot Menu - OKD Bootstrap ########
 label 1
 menu label ^1) Install OKD Bootstrap Node (FCOS)
 menu default
 kernel fcos/kernel
 append initrd=fcos/initramfs.img nomodeset rd.neednet=1 console=tty0 console=ttyS0 ip={{ ipconfig }} coreos.inst=yes coreos.inst.install_dev={{ disk }} coreos.live.rootfs_url=http://{{ helper.ipaddr }}:8080/fcos/rootfs.img coreos.inst.ignition_url=http://{{ helper.ipaddr }}:8080/ignition/bootstrap.ign

