authoritative;
ddns-update-style interim;
default-lease-time 14400;
max-lease-time 14400;
allow booting;
allow bootp;

    option routers                  {{ dhcp.router }};
    option broadcast-address        {{ dhcp.bcast }};
    option subnet-mask              {{ dhcp.netmask }};
{% if dhcp.dns is defined and dhcp.dns != "" %}
    option domain-name-servers      {{ dhcp.dns }};
{% else %}
    option domain-name-servers      {{ helper.ipaddr }};
{% endif %}
{% if dhcp.ntp is defined and dhcp.ntp != "" %}
    option ntp-servers              {{ dhcp.ntp }};
{% endif %}
    option domain-name              "{{ dns.clusterid }}.{{ dns.domain | lower }}";
    option domain-search            "{{ dns.clusterid }}.{{ dns.domain | lower }}", "{{ dns.domain | lower }}";

    subnet {{ dhcp.ipid }} netmask {{ dhcp.netmaskid }} {
    interface {{ helper.networkifacename }};
        pool {
            range {{ dhcp.poolstart }} {{ dhcp.poolend }};
        # Static entries
{% if bootstrap is defined %}
        host {{ bootstrap.name | lower }} { hardware ethernet {{ bootstrap.macaddr }}; fixed-address {{ bootstrap.ipaddr }}; }
{% endif %}
{% for m in masters %}
        host {{ m.name | lower }} { hardware ethernet {{ m.macaddr }}; fixed-address {{ m.ipaddr }}; }
{% endfor %}
{% if workers is defined %}
{% for w in workers %}
        host {{ w.name | lower }} { hardware ethernet {{ w.macaddr }}; fixed-address {{ w.ipaddr }}; }
{% endfor %}
{% endif %}
{% if other is defined %}
{% for o in other %}
        host {{ o.name }} { hardware ethernet {{ o.macaddr }}; fixed-address {{ o.ipaddr }}; }
{% endfor %}
{% endif %}
        # this will not give out addresses to hosts not listed above
        deny unknown-clients;

        # this is PXE specific
{% if ppc64le is sameas true %}
        filename "boot/grub2/powerpc-ieee1275/core.elf";
{% else %}
        filename "pxelinux.0";
{% endif %}

        next-server {{ helper.ipaddr }};
        }
}
