$TTL 1W
@	IN	SOA	ns1.{{ dns.clusterid }}.{{ dns.domain | lower }}.	root (
			{{ serialnumber }}	; serial
			3H		; refresh (3 hours)
			30M		; retry (30 minutes)
			2W		; expiry (2 weeks)
			1W )		; minimum (1 week)
	IN	NS	ns1.{{ dns.clusterid }}.{{ dns.domain | lower }}.
;
; syntax is "last octet" and the host must have fqdn with trailing dot
{{ helper.ipaddr.split('.')[3] }}       IN      PTR     helper.{{ dns.clusterid }}.{{ dns.domain }}.

{% for m in masters %}
{{ m.ipaddr.split('.')[3] }}	IN	PTR	{{ m.name | lower }}.{{ dns.clusterid }}.{{ dns.domain | lower }}.
{% endfor %}
;
{% if bootstrap is defined %}
{{ bootstrap.ipaddr.split('.')[3] }}	IN	PTR	{{ bootstrap.name | lower  }}.{{ dns.clusterid }}.{{ dns.domain | lower }}.
;
{% endif %}
{% if dns.lb_ipaddr is not defined or dns.lb_ipaddr == helper.ipaddr %}
{{ helper.ipaddr.split('.')[3] }}	IN	PTR	api.{{ dns.clusterid }}.{{ dns.domain | lower }}.
{{ helper.ipaddr.split('.')[3] }}	IN	PTR	api-int.{{ dns.clusterid }}.{{ dns.domain | lower }}.
{% endif %}
;
{% if workers is defined %}
{% for w in workers %}
{{ w.ipaddr.split('.')[3] }}	IN	PTR	{{ w.name | lower }}.{{ dns.clusterid }}.{{ dns.domain | lower }}.
{% endfor %}
{% endif %}
;
{% if other is defined %}
{% for o in other %}
{{ o.ipaddr.split('.')[3] }}	IN	PTR	{{ o.name }}.{{ dns.clusterid }}.{{ dns.domain }}.
{% endfor %}
;
{% endif %}
;EOF
