---
# Ansible Playbook untuk Otomatisasi Install OKD 4.19 dengan FCOS
# Playbook ini akan mengotomatisasi pembuatan VMs dan konfigurasi services

- name: Setup OKD 4.19 Infrastructure
  hosts: localhost
  become: yes
  vars:
    # Network Configuration
    network_name: "openshift4"
    network_bridge: "openshift4"
    network_cidr: "*************/24"
    network_gateway: "*************"
    
    # Bastion VM Configuration
    bastion_name: "okd-bastion-server"
    bastion_ip: "***************"
    bastion_ram: 4096
    bastion_vcpus: 2
    bastion_disk_size: 20
    
    # OKD Cluster Configuration
    cluster_domain: "febryan.web.id.example"
    cluster_name: "okd4"
    
    # VM Storage Pool
    storage_pool: "default"
    
    # FCOS Images URLs (Latest)
    fcos_kernel_url: "https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-live-kernel.x86_64"
    fcos_initramfs_url: "https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-live-initramfs.x86_64.img"
    fcos_rootfs_url: "https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-live-rootfs.x86_64.img"
    
    # OKD Installer URLs (Latest)
    okd_client_url: "https://github.com/okd-project/okd/releases/download/4.19.0-okd-scos.10/openshift-client-linux-4.19.0-okd-scos.10.tar.gz"
    okd_installer_url: "https://github.com/okd-project/okd/releases/download/4.19.0-okd-scos.10/openshift-install-linux-4.19.0-okd-scos.10.tar.gz"
    
    # Cluster VMs Configuration
    cluster_vms:
      bootstrap:
        name: "bootstrap.{{ cluster_name }}.{{ cluster_domain }}"
        ip: "*************0"
        mac: "52:54:00:a4:db:5f"
        ram: 8192
        vcpus: 4
        disk_size: 50
      masters:
        - name: "master01.{{ cluster_name }}.{{ cluster_domain }}"
          ip: "*************1"
          mac: "52:54:00:8b:a1:17"
          ram: 8192
          vcpus: 4
          disk_size: 50
        - name: "master02.{{ cluster_name }}.{{ cluster_domain }}"
          ip: "*************2"
          mac: "52:54:00:ea:8b:9d"
          ram: 8192
          vcpus: 4
          disk_size: 50
        - name: "master03.{{ cluster_name }}.{{ cluster_domain }}"
          ip: "*************3"
          mac: "52:54:00:f8:87:c7"
          ram: 8192
          vcpus: 4
          disk_size: 50
      workers:
        - name: "worker01.{{ cluster_name }}.{{ cluster_domain }}"
          ip: "**************"
          mac: "52:54:00:31:4a:39"
          ram: 8192
          vcpus: 2
          disk_size: 50
        - name: "worker02.{{ cluster_name }}.{{ cluster_domain }}"
          ip: "**************"
          mac: "52:54:00:6a:37:32"
          ram: 8192
          vcpus: 2
          disk_size: 50
        - name: "worker03.{{ cluster_name }}.{{ cluster_domain }}"
          ip: "**************"
          mac: "52:54:00:95:d4:ed"
          ram: 8192
          vcpus: 2
          disk_size: 50

  tasks:
    # ========================================
    # 1. Install Required Packages
    # ========================================
    - name: Install KVM and virtualization packages
      package:
        name:
          - qemu-kvm
          - libvirt
          - virt-install
          - virt-manager
          - virt-builder
          - bridge-utils
          - python3-libvirt
          - python3-lxml
        state: present

    - name: Start and enable libvirtd
      systemd:
        name: libvirtd
        state: started
        enabled: yes

    # ========================================
    # 2. Create Virtual Network
    # ========================================
    - name: Create virtual network XML
      copy:
        content: |
          <network>
            <name>{{ network_name }}</name>
            <forward mode='nat'>
              <nat>
                <port start='1024' end='65535'/>
              </nat>
            </forward>
            <bridge name='{{ network_bridge }}' stp='on' delay='0'/>
            <domain name='{{ network_name }}'/>
            <ip address='{{ network_gateway }}' netmask='*************'>
            </ip>
          </network>
        dest: "/tmp/{{ network_name }}.xml"

    - name: Define virtual network
      virt_net:
        command: define
        name: "{{ network_name }}"
        xml: "{{ lookup('file', '/tmp/' + network_name + '.xml') }}"

    - name: Start and autostart virtual network
      virt_net:
        name: "{{ network_name }}"
        state: active
        autostart: yes

    # ========================================
    # 3. Create Bastion VM
    # ========================================
    - name: Check if bastion VM image exists
      stat:
        path: "/var/lib/libvirt/images/{{ bastion_name }}.qcow2"
      register: bastion_image

    - name: Create bastion VM image with virt-builder
      command: >
        virt-builder fedora-42 --format qcow2
        --size {{ bastion_disk_size }}G
        -o /var/lib/libvirt/images/{{ bastion_name }}.qcow2
        --root-password password:rahasia
      when: not bastion_image.stat.exists

    - name: Create bastion VM
      virt:
        name: "{{ bastion_name }}"
        command: define
        xml: |
          <domain type='kvm'>
            <name>{{ bastion_name }}</name>
            <memory unit='MiB'>{{ bastion_ram }}</memory>
            <vcpu>{{ bastion_vcpus }}</vcpu>
            <os>
              <type arch='x86_64' machine='pc'>hvm</type>
              <boot dev='hd'/>
            </os>
            <devices>
              <disk type='file' device='disk'>
                <driver name='qemu' type='qcow2'/>
                <source file='/var/lib/libvirt/images/{{ bastion_name }}.qcow2'/>
                <target dev='vda' bus='virtio'/>
              </disk>
              <interface type='bridge'>
                <source bridge='{{ network_bridge }}'/>
                <model type='virtio'/>
              </interface>
              <serial type='pty'>
                <target port='0'/>
              </serial>
              <console type='pty'>
                <target type='serial' port='0'/>
              </console>
              <graphics type='none'/>
            </devices>
          </domain>

    - name: Start bastion VM
      virt:
        name: "{{ bastion_name }}"
        state: running

    - name: Set bastion VM to autostart
      virt:
        name: "{{ bastion_name }}"
        autostart: yes

    # ========================================
    # 4. Wait for Bastion VM to be Ready
    # ========================================
    - name: Wait for bastion VM to be accessible
      wait_for:
        host: "{{ bastion_ip }}"
        port: 22
        delay: 60
        timeout: 300
      ignore_errors: yes

    - name: Display bastion VM connection info
      debug:
        msg: |
          Bastion VM created successfully!
          
          To connect to bastion VM:
          sudo virsh console {{ bastion_name }}
          
          Login credentials:
          Username: root
          Password: rahasia
          
          Next steps:
          1. Configure network in bastion VM:
             nmcli con add type ethernet con-name enp1s0 ifname enp1s0 \
               connection.autoconnect yes ipv4.method manual \
               ipv4.address {{ bastion_ip }}/24 ipv4.gateway {{ network_gateway }} \
               ipv4.dns *******
          
          2. Update system and install required packages
          3. Clone ocp4_ansible repository
          4. Follow the tutorial to setup services

    # ========================================
    # 5. Create Cluster VM Definitions (without starting)
    # ========================================
    - name: Create bootstrap VM definition
      virt:
        name: "{{ cluster_vms.bootstrap.name }}"
        command: define
        xml: |
          <domain type='kvm'>
            <name>{{ cluster_vms.bootstrap.name }}</name>
            <description>Bootstrap Machine for OKD 4.19 Cluster</description>
            <memory unit='MiB'>{{ cluster_vms.bootstrap.ram }}</memory>
            <vcpu>{{ cluster_vms.bootstrap.vcpus }}</vcpu>
            <os>
              <type arch='x86_64' machine='pc'>hvm</type>
              <boot dev='network'/>
              <boot dev='hd'/>
            </os>
            <devices>
              <disk type='file' device='disk'>
                <driver name='qemu' type='qcow2'/>
                <source file='/var/lib/libvirt/images/{{ cluster_vms.bootstrap.name }}.qcow2'/>
                <target dev='vda' bus='virtio'/>
              </disk>
              <interface type='bridge'>
                <mac address='{{ cluster_vms.bootstrap.mac }}'/>
                <source bridge='{{ network_bridge }}'/>
                <model type='virtio'/>
              </interface>
              <serial type='pty'>
                <target port='0'/>
              </serial>
              <console type='pty'>
                <target type='serial' port='0'/>
              </console>
              <graphics type='none'/>
            </devices>
          </domain>

    - name: Create bootstrap VM disk
      command: >
        qemu-img create -f qcow2
        /var/lib/libvirt/images/{{ cluster_vms.bootstrap.name }}.qcow2
        {{ cluster_vms.bootstrap.disk_size }}G

    - name: Create master VMs definitions
      virt:
        name: "{{ item.name }}"
        command: define
        xml: |
          <domain type='kvm'>
            <name>{{ item.name }}</name>
            <description>Master Machine for OKD 4.19 Cluster</description>
            <memory unit='MiB'>{{ item.ram }}</memory>
            <vcpu>{{ item.vcpus }}</vcpu>
            <os>
              <type arch='x86_64' machine='pc'>hvm</type>
              <boot dev='network'/>
              <boot dev='hd'/>
            </os>
            <devices>
              <disk type='file' device='disk'>
                <driver name='qemu' type='qcow2'/>
                <source file='/var/lib/libvirt/images/{{ item.name }}.qcow2'/>
                <target dev='vda' bus='virtio'/>
              </disk>
              <interface type='bridge'>
                <mac address='{{ item.mac }}'/>
                <source bridge='{{ network_bridge }}'/>
                <model type='virtio'/>
              </interface>
              <serial type='pty'>
                <target port='0'/>
              </serial>
              <console type='pty'>
                <target type='serial' port='0'/>
              </console>
              <graphics type='none'/>
            </devices>
          </domain>
      loop: "{{ cluster_vms.masters }}"

    - name: Create master VMs disks
      command: >
        qemu-img create -f qcow2
        /var/lib/libvirt/images/{{ item.name }}.qcow2
        {{ item.disk_size }}G
      loop: "{{ cluster_vms.masters }}"

    - name: Create worker VMs definitions
      virt:
        name: "{{ item.name }}"
        command: define
        xml: |
          <domain type='kvm'>
            <name>{{ item.name }}</name>
            <description>Worker Machine for OKD 4.19 Cluster</description>
            <memory unit='MiB'>{{ item.ram }}</memory>
            <vcpu>{{ item.vcpus }}</vcpu>
            <os>
              <type arch='x86_64' machine='pc'>hvm</type>
              <boot dev='network'/>
              <boot dev='hd'/>
            </os>
            <devices>
              <disk type='file' device='disk'>
                <driver name='qemu' type='qcow2'/>
                <source file='/var/lib/libvirt/images/{{ item.name }}.qcow2'/>
                <target dev='vda' bus='virtio'/>
              </disk>
              <interface type='bridge'>
                <mac address='{{ item.mac }}'/>
                <source bridge='{{ network_bridge }}'/>
                <model type='virtio'/>
              </interface>
              <serial type='pty'>
                <target port='0'/>
              </serial>
              <console type='pty'>
                <target type='serial' port='0'/>
              </console>
              <graphics type='none'/>
            </devices>
          </domain>
      loop: "{{ cluster_vms.workers }}"

    - name: Create worker VMs disks
      command: >
        qemu-img create -f qcow2
        /var/lib/libvirt/images/{{ item.name }}.qcow2
        {{ item.disk_size }}G
      loop: "{{ cluster_vms.workers }}"

    # ========================================
    # 6. Display Summary
    # ========================================
    - name: Display deployment summary
      debug:
        msg: |
          ========================================
          OKD 4.19 Infrastructure Setup Complete!
          ========================================
          
          Network Configuration:
          - Network: {{ network_name }} ({{ network_cidr }})
          - Gateway: {{ network_gateway }}
          - Bridge: {{ network_bridge }}
          
          Bastion VM:
          - Name: {{ bastion_name }}
          - IP: {{ bastion_ip }}
          - Status: Running
          
          Cluster VMs Created (Not Started):
          - Bootstrap: {{ cluster_vms.bootstrap.name }}
          - Masters: {{ cluster_vms.masters | map(attribute='name') | join(', ') }}
          - Workers: {{ cluster_vms.workers | map(attribute='name') | join(', ') }}
          
          Next Steps:
          1. Connect to bastion VM and configure network
          2. Setup services (DHCP, DNS, TFTP, HAProxy) in bastion
          3. Download FCOS images and OKD installer
          4. Generate ignition files
          5. Start cluster VMs for installation
          
          Use the tutorial files for detailed instructions:
          - tutorial-okd-4.19-lengkap.md
          - tutorial-okd-4.19-lengkap.html
