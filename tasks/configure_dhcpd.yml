---
# Setup OCP4 DHCP Server on Helper Node

- hosts: all
  vars_files:
    - ../vars/main.yml
  handlers:
  - import_tasks: ../handlers/main.yml

  tasks:
  - name: Write out dhcp file
    ansible.builtin.template:
      src: ../templates/dhcpd.conf.j2
      dest: /etc/dhcp/dhcpd.conf
    notify:
      - restart dhcpd
    when: not uefi

  - name: Write out dhcp file (UEFI)
    ansible.builtin.template:
      src: ../templates/dhcpd-uefi.conf.j2
      dest: /etc/dhcp/dhcpd.conf
    notify:
      - restart dhcpd
    when: uefi
  
