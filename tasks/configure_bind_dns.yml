---
# Configure OCP4 DNS Server on Helper Node

- hosts: all
  vars_files:
    - ../vars/main.yml
  handlers:
  - import_tasks: ../handlers/main.yml

  tasks:
  - name: Setup named configuration files
    block:
    - name: Write out named file
      ansible.builtin.template:
        src: ../templates/named.conf.j2
        dest: /etc/named.conf
      notify:
        - restart bind
    - name: Set zone serial number
      ansible.builtin.shell: "/usr/local/bin/set-dns-serial.sh"
      register: dymanicserialnumber

    - name: Setting serial number as a fact
      ansible.builtin.set_fact:
        serialnumber: "{{ dymanicserialnumber.stdout }}"

    - name: Write out "{{ dns.domain | lower }}" zone file
      ansible.builtin.template:
        src: ../templates/zonefile.j2
        dest: /var/named/zonefile.db
        mode: '0644'
      notify:
        - restart bind

    - name: Write out reverse zone file
      ansible.builtin.template:
        src: ../templates/reverse.j2
        dest: /var/named/reverse.db
        mode: '0644'
      notify:
        - restart bind
