===host===
vim openshift4.xml

<network>
  <name>openshift4</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='openshift4' stp='on' delay='0'/>
  <domain name='openshift4'/>
  <ip address='*************' netmask='*************'>
  </ip>
</network>

sudo virsh net-define --file openshift4.xml
sudo virsh net-autostart openshift4


sudo virsh net-start openshift4

sudo virt-builder -l

sudo virt-builder fedora-42  --format qcow2 \
  --size 20G -o /var/lib/libvirt/images/ocp-bastion-server.qcow2 \
  --root-password password:rahasia

sudo virsh console ocp-bastion-server
root:rahasia


sudo virt-install \
  --name ocp-bastion-server \
  --ram 4096 \
  --disk path=/var/lib/libvirt/images/ocp-bastion-server.qcow2 \
  --vcpus 2 \
  --os-variant rhel9.0 \
  --network=bridge:openshift4,model=virtio \
  --graphics none \
  --serial pty \
  --console pty \
  --boot hd \
  --import


===VM Bastion===

nmcli con add type ethernet con-name enp1s0 ifname enp1s0 \
  connection.autoconnect yes ipv4.method manual \
  ipv4.address ***************/24 ipv4.gateway ************* \
  ipv4.dns *******

tes ping *******

sudo dnf -y upgrade
sudo dnf -y install git vim wget curl bash-completion tree tar libselinux-python3 firewalld

sudo reboot

sudo virsh autostart ocp-bastion-server


# Fedora
sudo dnf -y install git ansible vim wget curl bash-completion tree tar libselinux-python3

# CentOS 8 / Rocky Linux 8
sudo yum -y install epel-release
sudo yum -y install git ansible vim wget curl bash-completion tree tar libselinux-python3

# CentOS 7
sudo yum -y install epel-release
sudo yum -y install git ansible vim wget curl bash-completion tree tar libselinux-python

===Bastion ansible dhcp, dns server===
cd ~/
git clone https://github.com/jmutai/ocp4_ansible.git
cd ~/ocp4_ansible

[root@fedora ~]# git clone https://github.com/jmutai/ocp4_ansible.git
Cloning into 'ocp4_ansible'...
remote: Enumerating objects: 82, done.
remote: Counting objects: 100% (44/44), done.
remote: Compressing objects: 100% (23/23), done.
remote: Total 82 (delta 23), reused 26 (delta 17), pack-reused 38 (from 1)
Receiving objects: 100% (82/82), 30.64 KiB | 2.04 MiB/s, done.
Resolving deltas: 100% (31/31), done.
[root@fedora ~]# cd ~/ocp4_ansible
[root@fedora ocp4_ansible]# tree
.
├── ansible.cfg
├── files
│   └── set-dns-serial.sh
├── handlers
│   └── main.yml
├── inventory
├── LICENSE
├── README.md
├── tasks
│   ├── configure_bind_dns.yml
│   ├── configure_dhcpd.yml
│   ├── configure_haproxy_lb.yml
│   └── configure_tftp_pxe.yml
├── templates
│   ├── default.j2
│   ├── dhcpd.conf.j2
│   ├── dhcpd-uefi.conf.j2
│   ├── haproxy.cfg.j2
│   ├── named.conf.j2
│   ├── pxe-bootstrap.j2
│   ├── pxe-master.j2
│   ├── pxe-worker.j2
│   ├── reverse.j2
│   └── zonefile.j2
└── vars
    └── main.yml

vim vars/main.yml

---
ppc64le: false
uefi: false
disk: vda                                  #disk where you are installing RHCOS on the masters/workers
helper:
  name: "bastion"                          #hostname for your helper node
  ipaddr: "***************"                #current IP address of the helper
  networkifacename: "enp1s0"               #interface of the helper node,ACTUAL name of the interface, NOT the NetworkManager name, check with $ ip ad
dns:
  domain: "febryan.web.id.example"         #DNS server domain. Should match  baseDomain inside the install-config.yaml file.
  clusterid: "ocp4"                        #needs to match what you will for metadata.name inside the install-config.yaml file
  forwarder1: "*******"                    #DNS forwarder
  forwarder2: "*******"                    #second DNS forwarder
  lb_ipaddr: "{{ helper.ipaddr }}"         #Load balancer IP, it is optional, the default value is helper.ipaddr
dhcp:
  router: "*************"                  #default gateway of the network assigned to the masters/workers
  bcast: "***************"                 #broadcast address for your network
  netmask: "*************"                 #netmask that gets assigned to your masters/workers
  poolstart: "**************"              #First address in your dhcp address pool
  poolend: "**************"                #Last address in your dhcp address pool
  ipid: "*************"                    #ip network id for the range
  netmaskid: "*************"               #networkmask id for the range.
  ntp: "time.google.com"                   #ntp server address
  dns: ""                                  #domain name server, it is optional, the default value is set to helper.ipaddr
bootstrap:
  name: "bootstrap"                        #hostname (WITHOUT the fqdn) of the bootstrap node 
  ipaddr: "**************"                 #IP address that you want set for bootstrap node
  macaddr: "52:54:00:a4:db:5f"             #The mac address for dhcp reservation
masters:
  - name: "master01"                       #hostname (WITHOUT the fqdn) of the master node (x of 3)
    ipaddr: "**************"               #The IP address (x of 3) that you want set
    macaddr: "52:54:00:8b:a1:17"           #The mac address for dhcp reservation
  - name: "master02"
    ipaddr: "**************"
    macaddr: "52:54:00:ea:8b:9d"
  - name: "master03"
    ipaddr: "**************"
    macaddr: "52:54:00:f8:87:c7"
workers:
  - name: "worker01"                       #hostname (WITHOUT the fqdn) of the worker node you want to set
    ipaddr: "**************"               #The IP address that you want set (1st node)
    macaddr: "52:54:00:31:4a:39"           #The mac address for dhcp reservation (1st node)
  - name: "worker02"
    ipaddr: "**************"
    macaddr: "52:54:00:6a:37:32"
  - name: "worker03"
    ipaddr: "**************"
    macaddr: "52:54:00:95:d4:ed"

===bastion dhcp server===
sudo yum -y install dhcp-server
sudo systemctl enable dhcpd
sudo mv /etc/dhcp/dhcpd.conf /etc/dhcp/dhcpd.conf.bak

ansible-playbook tasks/configure_dhcpd.yml
verif: 
systemctl status dhcpd
cat /etc/dhcp/dhcpd.conf

[root@fedora ocp4_ansible]#  cat /etc/dhcp/dhcpd.conf
authoritative;
ddns-update-style interim;
default-lease-time 14400;
max-lease-time 14400;
allow booting;
allow bootp;

    option routers                  *************;
    option broadcast-address        ***************;
    option subnet-mask              *************;
    option domain-name-servers      ***************;
    option ntp-servers              time.google.com;
    option domain-name              "ocp4.febryan.web.id.example";
    option domain-search            "ocp4.febryan.web.id.example", "febryan.web.id.example";

    subnet ************* netmask ************* {
    interface enp1s0;
        pool {
            range ************** **************;
        # Static entries
        host bootstrap { hardware ethernet 52:54:00:a4:db:5f; fixed-address **************; }
        host master01 { hardware ethernet 52:54:00:8b:a1:17; fixed-address **************; }
        host master02 { hardware ethernet 52:54:00:ea:8b:9d; fixed-address **************; }
        host master03 { hardware ethernet 52:54:00:f8:87:c7; fixed-address **************; }
        host worker01 { hardware ethernet 52:54:00:31:4a:39; fixed-address **************; }
        host worker02 { hardware ethernet 52:54:00:6a:37:32; fixed-address **************; }
        host worker03 { hardware ethernet 52:54:00:95:d4:ed; fixed-address **************; }
        # this will not give out addresses to hosts not listed above
        deny unknown-clients;

        # this is PXE specific
        filename "pxelinux.0";

        next-server ***************;
        }
}


===Bastion DNS Server===
sudo yum -y install bind bind-utils
sudo systemctl enable named

sudo vim /usr/local/bin/set-dns-serial.sh
#!/bin/bash
dnsserialfile=/usr/local/src/dnsserial-DO_NOT_DELETE_BEFORE_ASKING_CHRISTIAN.txt
zonefile=/var/named/zonefile.db
if [ -f zonefile ] ; then
	echo $[ $(grep serial ${zonefile}  | tr -d "\t"" ""\n"  | cut -d';' -f 1) + 1 ] | tee ${dnsserialfile}
else
	if [ ! -f ${dnsserialfile} ] || [ ! -s ${dnsserialfile} ]; then
		echo $(date +%Y%m%d00) | tee ${dnsserialfile}
	else
		echo $[ $(< ${dnsserialfile}) + 1 ] | tee ${dnsserialfile}
	fi
fi
##
##-30-

sudo chmod a+x /usr/local/bin/set-dns-serial.sh

ansible-playbook tasks/configure_bind_dns.yml
systemctl status named




[root@fedora ocp4_ansible]# dig @127.0.0.1 -t srv _etcd-server-ssl._tcp.ocp4.febryan.web.id.example

; <<>> DiG 9.18.36 <<>> @127.0.0.1 -t srv _etcd-server-ssl._tcp.ocp4.febryan.web.id.example
; (1 server found)
;; global options: +cmd
;; Got answer:
;; ->>HEADER<<- opcode: QUERY, status: NOERROR, id: 19036
;; flags: qr aa rd ra; QUERY: 1, ANSWER: 3, AUTHORITY: 0, ADDITIONAL: 4

;; OPT PSEUDOSECTION:
; EDNS: version: 0, flags:; udp: 1232
; COOKIE: fe840fdfdbc2b38901000000688cadc320e46a13880ede1c (good)
;; QUESTION SECTION:
;_etcd-server-ssl._tcp.ocp4.febryan.web.id.example. IN SRV

;; ANSWER SECTION:
_etcd-server-ssl._tcp.ocp4.febryan.web.id.example. 604800 IN SRV 0 10 2380 etcd-1.ocp4.febryan.web.id.example.
_etcd-server-ssl._tcp.ocp4.febryan.web.id.example. 604800 IN SRV 0 10 2380 etcd-0.ocp4.febryan.web.id.example.
_etcd-server-ssl._tcp.ocp4.febryan.web.id.example. 604800 IN SRV 0 10 2380 etcd-2.ocp4.febryan.web.id.example.

;; ADDITIONAL SECTION:
etcd-0.ocp4.febryan.web.id.example. 604800 IN A **************
etcd-1.ocp4.febryan.web.id.example. 604800 IN A **************
etcd-2.ocp4.febryan.web.id.example. 604800 IN A **************

;; Query time: 1 msec
;; SERVER: 127.0.0.1#53(127.0.0.1) (UDP)
;; WHEN: Fri Aug 01 08:06:27 EDT 2025
;; MSG SIZE  rcvd: 316

[root@fedora ocp4_ansible]# nmcli connection show
NAME    UUID                                  TYPE      DEVICE 
enp1s0  fed6f27a-b146-449d-9270-a5717af22afd  ethernet  enp1s0
lo      dc41df25-2fd9-409f-9dfe-d927d20ab70f  loopback  lo


nmcli connection modify enp1s0  ipv4.dns "***************"
[root@fedora ocp4_ansible]# nmcli connection modify enp1s0  ipv4.dns "***************"
[root@fedora ocp4_ansible]# nmcli connection reload

[root@fedora ocp4_ansible]# nmcli connection up enp1s0
Connection successfully activated (D-Bus active path: /org/freedesktop/NetworkManager/ActiveConnection/3)

[root@fedora ocp4_ansible]# host bootstrap.ocp4.febryan.web.id.example
bootstrap.ocp4.febryan.web.id.example has address **************

sudo firewall-cmd --add-service={dhcp,tftp,http,https,dns} --permanent
sudo firewall-cmd --reload

===bastion tftp server===
sudo yum -y install tftp-server syslinux
sudo firewall-cmd --add-service=tftp --permanent
sudo firewall-cmd --reload


sudo vim /etc/systemd/system/helper-tftp.service
[Unit]
Description=Starts TFTP on boot because of reasons
After=network.target

[Service]
Type=simple
ExecStart=/usr/local/bin/start-tftp.sh
TimeoutStartSec=0
Restart=always
RestartSec=30

[Install]
WantedBy=default.target


sudo tee /usr/local/bin/start-tftp.sh<<EOF
#!/bin/bash
/usr/bin/systemctl start tftp > /dev/null 2>&1
##
##
EOF

sudo chmod a+x /usr/local/bin/start-tftp.sh
sudo systemctl daemon-reload
sudo systemctl enable --now tftp helper-tftp
sudo mkdir -p  /var/lib/tftpboot/pxelinux.cfg
sudo cp -rvf /usr/share/syslinux/* /var/lib/tftpboot
sudo mkdir -p /var/lib/tftpboot/rhcos


[root@fedora ocp4_ansible]# sudo systemctl enable --now tftp helper-tftp
Created symlink '/etc/systemd/system/sockets.target.wants/tftp.socket' → '/usr/lib/systemd/system/tftp.socket'.
Created symlink '/etc/systemd/system/default.target.wants/helper-tftp.service' → '/etc/systemd/system/helper-tftp.service'.
[root@fedora ocp4_ansible]# sudo mkdir -p  /var/lib/tftpboot/pxelinux.cfg
[root@fedora ocp4_ansible]# sudo cp -rvf /usr/share/syslinux/* /var/lib/tftpboot
'/usr/share/syslinux/altmbr.bin' -> '/var/lib/tftpboot/altmbr.bin'
'/usr/share/syslinux/altmbr_c.bin' -> '/var/lib/tftpboot/altmbr_c.bin'
'/usr/share/syslinux/altmbr_f.bin' -> '/var/lib/tftpboot/altmbr_f.bin'
'/usr/share/syslinux/cat.c32' -> '/var/lib/tftpboot/cat.c32'
'/usr/share/syslinux/chain.c32' -> '/var/lib/tftpboot/chain.c32'
'/usr/share/syslinux/cmd.c32' -> '/var/lib/tftpboot/cmd.c32'
'/usr/share/syslinux/cmenu.c32' -> '/var/lib/tftpboot/cmenu.c32'
'/usr/share/syslinux/config.c32' -> '/var/lib/tftpboot/config.c32'
'/usr/share/syslinux/cptime.c32' -> '/var/lib/tftpboot/cptime.c32'
'/usr/share/syslinux/cpu.c32' -> '/var/lib/tftpboot/cpu.c32'
'/usr/share/syslinux/cpuid.c32' -> '/var/lib/tftpboot/cpuid.c32'
'/usr/share/syslinux/cpuidtest.c32' -> '/var/lib/tftpboot/cpuidtest.c32'
'/usr/share/syslinux/debug.c32' -> '/var/lib/tftpboot/debug.c32'
'/usr/share/syslinux/dhcp.c32' -> '/var/lib/tftpboot/dhcp.c32'
'/usr/share/syslinux/diag' -> '/var/lib/tftpboot/diag'
'/usr/share/syslinux/diag/geodsp1s.img.xz' -> '/var/lib/tftpboot/diag/geodsp1s.img.xz'
'/usr/share/syslinux/diag/geodspms.img.xz' -> '/var/lib/tftpboot/diag/geodspms.img.xz'
'/usr/share/syslinux/diag/handoff.bin' -> '/var/lib/tftpboot/diag/handoff.bin'
'/usr/share/syslinux/dir.c32' -> '/var/lib/tftpboot/dir.c32'
'/usr/share/syslinux/disk.c32' -> '/var/lib/tftpboot/disk.c32'
'/usr/share/syslinux/dmi.c32' -> '/var/lib/tftpboot/dmi.c32'
'/usr/share/syslinux/dmitest.c32' -> '/var/lib/tftpboot/dmitest.c32'
'/usr/share/syslinux/dosutil' -> '/var/lib/tftpboot/dosutil'
'/usr/share/syslinux/dosutil/copybs.com' -> '/var/lib/tftpboot/dosutil/copybs.com'
'/usr/share/syslinux/dosutil/eltorito.sys' -> '/var/lib/tftpboot/dosutil/eltorito.sys'
'/usr/share/syslinux/dosutil/mdiskchk.com' -> '/var/lib/tftpboot/dosutil/mdiskchk.com'
'/usr/share/syslinux/elf.c32' -> '/var/lib/tftpboot/elf.c32'
'/usr/share/syslinux/ethersel.c32' -> '/var/lib/tftpboot/ethersel.c32'
'/usr/share/syslinux/gfxboot.c32' -> '/var/lib/tftpboot/gfxboot.c32'
'/usr/share/syslinux/gptmbr_f.bin' -> '/var/lib/tftpboot/gptmbr_f.bin'
'/usr/share/syslinux/gpxecmd.c32' -> '/var/lib/tftpboot/gpxecmd.c32'
'/usr/share/syslinux/hdt.c32' -> '/var/lib/tftpboot/hdt.c32'
'/usr/share/syslinux/ifcpu64.c32' -> '/var/lib/tftpboot/ifcpu64.c32'
'/usr/share/syslinux/ifcpu.c32' -> '/var/lib/tftpboot/ifcpu.c32'
'/usr/share/syslinux/ifmemdsk.c32' -> '/var/lib/tftpboot/ifmemdsk.c32'
'/usr/share/syslinux/ifplop.c32' -> '/var/lib/tftpboot/ifplop.c32'
'/usr/share/syslinux/isohdpfx.bin' -> '/var/lib/tftpboot/isohdpfx.bin'
'/usr/share/syslinux/isohdpfx_c.bin' -> '/var/lib/tftpboot/isohdpfx_c.bin'
'/usr/share/syslinux/isohdpfx_f.bin' -> '/var/lib/tftpboot/isohdpfx_f.bin'
'/usr/share/syslinux/isohdppx.bin' -> '/var/lib/tftpboot/isohdppx.bin'
'/usr/share/syslinux/isohdppx_c.bin' -> '/var/lib/tftpboot/isohdppx_c.bin'
'/usr/share/syslinux/isohdppx_f.bin' -> '/var/lib/tftpboot/isohdppx_f.bin'
'/usr/share/syslinux/isolinux.bin' -> '/var/lib/tftpboot/isolinux.bin'
'/usr/share/syslinux/isolinux-debug.bin' -> '/var/lib/tftpboot/isolinux-debug.bin'
'/usr/share/syslinux/kbdmap.c32' -> '/var/lib/tftpboot/kbdmap.c32'
'/usr/share/syslinux/lfs.c32' -> '/var/lib/tftpboot/lfs.c32'
'/usr/share/syslinux/libcom32.c32' -> '/var/lib/tftpboot/libcom32.c32'
'/usr/share/syslinux/libgpl.c32' -> '/var/lib/tftpboot/libgpl.c32'
'/usr/share/syslinux/liblua.c32' -> '/var/lib/tftpboot/liblua.c32'
'/usr/share/syslinux/libmenu.c32' -> '/var/lib/tftpboot/libmenu.c32'
'/usr/share/syslinux/libutil.c32' -> '/var/lib/tftpboot/libutil.c32'
'/usr/share/syslinux/linux.c32' -> '/var/lib/tftpboot/linux.c32'
'/usr/share/syslinux/lua.c32' -> '/var/lib/tftpboot/lua.c32'
'/usr/share/syslinux/mboot.c32' -> '/var/lib/tftpboot/mboot.c32'
'/usr/share/syslinux/mbr.bin' -> '/var/lib/tftpboot/mbr.bin'
'/usr/share/syslinux/mbr_c.bin' -> '/var/lib/tftpboot/mbr_c.bin'
'/usr/share/syslinux/mbr_f.bin' -> '/var/lib/tftpboot/mbr_f.bin'
'/usr/share/syslinux/memdisk' -> '/var/lib/tftpboot/memdisk'
'/usr/share/syslinux/meminfo.c32' -> '/var/lib/tftpboot/meminfo.c32'
'/usr/share/syslinux/menu.c32' -> '/var/lib/tftpboot/menu.c32'
'/usr/share/syslinux/pci.c32' -> '/var/lib/tftpboot/pci.c32'
'/usr/share/syslinux/pcitest.c32' -> '/var/lib/tftpboot/pcitest.c32'
'/usr/share/syslinux/pmload.c32' -> '/var/lib/tftpboot/pmload.c32'
'/usr/share/syslinux/poweroff.c32' -> '/var/lib/tftpboot/poweroff.c32'
'/usr/share/syslinux/prdhcp.c32' -> '/var/lib/tftpboot/prdhcp.c32'
'/usr/share/syslinux/pwd.c32' -> '/var/lib/tftpboot/pwd.c32'
'/usr/share/syslinux/pxechn.c32' -> '/var/lib/tftpboot/pxechn.c32'
'/usr/share/syslinux/pxelinux.0' -> '/var/lib/tftpboot/pxelinux.0'
'/usr/share/syslinux/reboot.c32' -> '/var/lib/tftpboot/reboot.c32'
'/usr/share/syslinux/rosh.c32' -> '/var/lib/tftpboot/rosh.c32'
'/usr/share/syslinux/sanboot.c32' -> '/var/lib/tftpboot/sanboot.c32'
'/usr/share/syslinux/sdi.c32' -> '/var/lib/tftpboot/sdi.c32'
'/usr/share/syslinux/sysdump.c32' -> '/var/lib/tftpboot/sysdump.c32'
'/usr/share/syslinux/syslinux64.exe' -> '/var/lib/tftpboot/syslinux64.exe'
'/usr/share/syslinux/syslinux.c32' -> '/var/lib/tftpboot/syslinux.c32'
'/usr/share/syslinux/syslinux.com' -> '/var/lib/tftpboot/syslinux.com'
'/usr/share/syslinux/syslinux.exe' -> '/var/lib/tftpboot/syslinux.exe'
'/usr/share/syslinux/vesa.c32' -> '/var/lib/tftpboot/vesa.c32'
'/usr/share/syslinux/vesainfo.c32' -> '/var/lib/tftpboot/vesainfo.c32'
'/usr/share/syslinux/vesamenu.c32' -> '/var/lib/tftpboot/vesamenu.c32'
'/usr/share/syslinux/vpdtest.c32' -> '/var/lib/tftpboot/vpdtest.c32'
'/usr/share/syslinux/whichsys.c32' -> '/var/lib/tftpboot/whichsys.c32'
'/usr/share/syslinux/zzjson.c32' -> '/var/lib/tftpboot/zzjson.c32'


mkdir rhcos-image

[root@fedora rhcos-image]# wget https://mirror.openshift.com/pub/openshift-v4/dependencies/rhcos/latest/rhcos-installer-kernel.x86_64
Saving 'rhcos-installer-kernel.x86_64'
HTTP response 200  [https://mirror.openshift.com/pub/openshift-v4/dependencies/rhcos/latest/rhcos-installer-kernel.x86_64]
rhcos-installer-kern 100% [=========================================================================================================================================================================================================================================>]   14.22M    6.99MB/s 
                          [Files: 1  Bytes: 14.22M [3.70MB/s] Redirects: 0  Todo: 0  Errors: 0                                                                                                                                                            ]
[root@fedora rhcos-image]# wget https://mirror.openshift.com/pub/openshift-v4/dependencies/rhcos/latest/rhcos-installer-initramfs.x86_64.img
Saving 'rhcos-installer-initramfs.x86_64.img'
HTTP response 200  [https://mirror.openshift.com/pub/openshift-v4/dependencies/rhcos/latest/rhcos-installer-initramfs.x86_64.img]
rhcos-installer-init 100% [=========================================================================================================================================================================================================================================>]   96.94M    8.36MB/s
                          [Files: 1  Bytes: 96.94M [7.18MB/s] Redirects: 0  Todo: 0  Errors: 0  

[root@fedora rhcos-image]# sudo mv rhcos-installer-kernel.x86_64 /var/lib/tftpboot/rhcos/kernel
[root@fedora rhcos-image]# sudo mv rhcos-installer-initramfs.x86_64.img /var/lib/tftpboot/rhcos/initramfs.img

[root@fedora rhcos]# ls -lah
total 112M
drwxr-xr-x. 2 <USER> <GROUP>   41 Aug  1 08:20 .
drwxr-xr-x. 6 <USER> <GROUP> 4.0K Aug  1 08:13 ..
-rw-r--r--. 1 <USER> <GROUP>  97M Jun 23 11:18 initramfs.img
-rw-r--r--. 1 <USER> <GROUP>  15M Jun 23 11:18 kernel


[root@fedora rhcos-image]# sudo restorecon -RFv  /var/lib/tftpboot/rhcos
Relabeled /var/lib/tftpboot/rhcos from unconfined_u:object_r:tftpdir_rw_t:s0 to system_u:object_r:tftpdir_rw_t:s0
Relabeled /var/lib/tftpboot/rhcos/kernel from unconfined_u:object_r:admin_home_t:s0 to system_u:object_r:tftpdir_rw_t:s0
Relabeled /var/lib/tftpboot/rhcos/initramfs.img from unconfined_u:object_r:admin_home_t:s0 to system_u:object_r:tftpdir_rw_t:s0

===Bastion httpd===
sudo yum -y install httpd
sudo vim /etc/httpd/conf/httpd.conf

Search for the Line:
Listen 80

Change the line to:
Listen 8080

sudo rm /etc/httpd/conf.d/welcome.conf
sudo systemctl enable httpd
sudo systemctl restart httpd
sudo firewall-cmd --add-port=8080/tcp --permanent
sudo firewall-cmd --reload


sudo mkdir -p /var/www/html/rhcos

[root@fedora rhcos-image]# wget https://mirror.openshift.com/pub/openshift-v4/dependencies/rhcos/latest/rhcos-live-rootfs.x86_64.img
Saving 'rhcos-live-rootfs.x86_64.img'
HTTP response 200  [https://mirror.openshift.com/pub/openshift-v4/dependencies/rhcos/latest/rhcos-live-rootfs.x86_64.img]
rhcos-live-rootfs.x8 100% [=========================================================================================================================================================================================================================================>]    1.03G    8.33MB/s 
                          [Files: 1  Bytes: 1.03G [8.38MB/s] Redirects: 0  Todo: 0  Errors: 0                                                                                                                                                                        ]
[root@fedora rhcos-image]# 

[root@fedora rhcos-image]# sudo mv rhcos-live-rootfs.x86_64.img /var/www/html/rhcos/rootfs.img
[root@fedora rhcos-image]# sudo restorecon -RFv /var/www/html/rhcos
Relabeled /var/www/html/rhcos from unconfined_u:object_r:httpd_sys_content_t:s0 to system_u:object_r:httpd_sys_content_t:s0
Relabeled /var/www/html/rhcos/rootfs.img from unconfined_u:object_r:admin_home_t:s0 to system_u:object_r:httpd_sys_content_t:s0
[root@fedora rhcos-image]# 


ansible-playbook tasks/configure_tftp_pxe.yml
[root@fedora ocp4_ansible]# ansible-playbook tasks/configure_tftp_pxe.yml

PLAY [all] *********************************************************************************************************************************************************************************************************************************************************************************

TASK [Gathering Facts] *********************************************************************************************************************************************************************************************************************************************************************
[WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.13, but future installation of another Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.18/reference_appendices/interpreter_discovery.html for more information.
ok: [localhost]

TASK [Set the bootstrap specific tftp file] ************************************************************************************************************************************************************************************************************************************************
changed: [localhost]

TASK [Set the master specific tftp files] **************************************************************************************************************************************************************************************************************************************************
changed: [localhost] => (item={'name': 'master01', 'ipaddr': '**************', 'macaddr': '52:54:00:8b:a1:17'})
changed: [localhost] => (item={'name': 'master02', 'ipaddr': '**************', 'macaddr': '52:54:00:ea:8b:9d'})
changed: [localhost] => (item={'name': 'master03', 'ipaddr': '**************', 'macaddr': '52:54:00:f8:87:c7'})

TASK [Set the worker specific tftp files] **************************************************************************************************************************************************************************************************************************************************
changed: [localhost] => (item={'name': 'worker01', 'ipaddr': '**************', 'macaddr': '52:54:00:31:4a:39'})
changed: [localhost] => (item={'name': 'worker02', 'ipaddr': '**************', 'macaddr': '52:54:00:6a:37:32'})
changed: [localhost] => (item={'name': 'worker03', 'ipaddr': '**************', 'macaddr': '52:54:00:95:d4:ed'})

RUNNING HANDLER [restart tftp] *************************************************************************************************************************************************************************************************************************************************************
changed: [localhost]

PLAY RECAP *********************************************************************************************************************************************************************************************************************************************************************************
localhost                  : ok=5    changed=4    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0

[root@fedora ocp4_ansible]# 


[root@fedora ocp4_ansible]# cat /var/lib/tftpboot/pxelinux.cfg/01-52-54-00-
01-52-54-00-31-4a-39  01-52-54-00-6a-37-32  01-52-54-00-8b-a1-17  01-52-54-00-95-d4-ed  01-52-54-00-a4-db-5f  01-52-54-00-ea-8b-9d  01-52-54-00-f8-87-c7

[root@fedora ocp4_ansible]# ls -1 /var/lib/tftpboot/pxelinux.cfg
01-52-54-00-31-4a-39
01-52-54-00-6a-37-32
01-52-54-00-8b-a1-17
01-52-54-00-95-d4-ed
01-52-54-00-a4-db-5f
01-52-54-00-ea-8b-9d
01-52-54-00-f8-87-c7



===bastion haproxy===
[root@fedora ocp4_ansible]# sudo yum install -y haproxy
Updating and loading repositories:
Repositories loaded.
Package                                                                                            Arch                 Version                                                                                            Repository                                                   Size
Installing:
 haproxy                                                                                           x86_64               3.0.5-1.fc42                                                                                       fedora                                                    8.0 MiB

Transaction Summary:
 Installing:         1 package

Total size of inbound packages is 3 MiB. Need to download 3 MiB.
After this operation, 8 MiB extra will be used (install 8 MiB, remove 0 B).
[1/1] haproxy-0:3.0.5-1.fc42.x86_64                                                                                                                                                                                                                 100% |   1.3 MiB/s |   2.6 MiB |  00m02s
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
[1/1] Total                                                                                                                                                                                                                                         100% | 922.9 KiB/s |   2.6 MiB |  00m03s
Running transaction
[1/3] Verify package files                                                                                                                                                                                                                          100% |  76.0   B/s |   1.0   B |  00m00s
[2/3] Prepare transaction                                                                                                                                                                                                                           100% |   4.0   B/s |   1.0   B |  00m00s
>>> Running sysusers scriptlet: haproxy-0:3.0.5-1.fc42.x86_64
>>> Finished sysusers scriptlet: haproxy-0:3.0.5-1.fc42.x86_64
>>> Scriptlet output:
>>> Creating group 'haproxy' with GID 992.
>>> Creating user 'haproxy' (haproxy) with UID 992 and GID 992.
>>>
[3/3] Installing haproxy-0:3.0.5-1.fc42.x86_64                                                                                                                                                                                                      100% |   9.1 MiB/s |   8.0 MiB |  00m01s
Complete!                                                                                                                                                                                                                                                                                   
[root@fedora ocp4_ansible]# sudo setsebool -P haproxy_connect_any 1
[root@fedora ocp4_ansible]# 

sudo mv /etc/haproxy/haproxy.cfg /etc/haproxy/haproxy.cfg.default

ansible-playbook tasks/configure_haproxy_lb.yml
[root@fedora ocp4_ansible]# ansible-playbook tasks/configure_haproxy_lb.yml

PLAY [all] *********************************************************************************************************************************************************************************************************************************************************************************

TASK [Gathering Facts] *********************************************************************************************************************************************************************************************************************************************************************
[WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.13, but future installation of another Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.18/reference_appendices/interpreter_discovery.html for more information.
ok: [localhost]

TASK [Write out haproxy config file] *******************************************************************************************************************************************************************************************************************************************************
changed: [localhost]

RUNNING HANDLER [restart haproxy] **********************************************************************************************************************************************************************************************************************************************************
changed: [localhost]

PLAY RECAP *********************************************************************************************************************************************************************************************************************************************************************************
localhost                  : ok=3    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0



sudo dnf install -y policycoreutils-python-utils

sudo semanage port  -a 6443 -t http_port_t -p tcp
sudo semanage port  -a 22623 -t http_port_t -p tcp
sudo semanage port -a 32700 -t http_port_t -p tcp


[root@fedora ocp4_ansible]# sudo firewall-cmd --add-service={http,https} --permanent
Warning: ALREADY_ENABLED: http
Warning: ALREADY_ENABLED: https
success
[root@fedora ocp4_ansible]# sudo firewall-cmd --add-port={6443,22623}/tcp --permanent
success
[root@fedora ocp4_ansible]# sudo firewall-cmd --reload
success
[root@fedora ocp4_ansible]# 