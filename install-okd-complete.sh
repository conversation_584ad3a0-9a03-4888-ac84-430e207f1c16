#!/bin/bash

# Complete OKD 4.19 Installation Script
# Script ini mengotomatisasi seluruh proses instalasi OKD 4.19 dengan FCOS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASTION_NAME="okd-bastion-server"
BASTION_IP="***************"
CLUSTER_DOMAIN="febryan.web.id.example"
CLUSTER_NAME="okd4"

# Functions
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

check_requirements() {
    print_header "Checking Requirements"
    
    # Check if running as root or with sudo
    if [[ $EUID -eq 0 ]]; then
        print_warning "Running as root. This is OK for infrastructure setup."
    else
        print_info "Running as regular user. Will use sudo when needed."
    fi
    
    # Check available memory
    TOTAL_MEM=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $TOTAL_MEM -lt 24 ]]; then
        print_warning "System has ${TOTAL_MEM}GB RAM. Recommended: 32GB+ for full cluster."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        print_success "System has ${TOTAL_MEM}GB RAM. Good for OKD cluster."
    fi
    
    # Check available disk space
    AVAILABLE_SPACE=$(df -BG / | awk 'NR==2 {print $4}' | sed 's/G//')
    if [[ $AVAILABLE_SPACE -lt 200 ]]; then
        print_warning "Available disk space: ${AVAILABLE_SPACE}GB. Recommended: 500GB+"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        print_success "Available disk space: ${AVAILABLE_SPACE}GB. Sufficient for OKD cluster."
    fi
    
    # Check virtualization support
    if grep -q -E "(vmx|svm)" /proc/cpuinfo; then
        print_success "CPU virtualization support detected."
    else
        print_error "No CPU virtualization support detected. Cannot proceed."
        exit 1
    fi
    
    # Check if KVM is available
    if [[ -e /dev/kvm ]]; then
        print_success "KVM device available."
    else
        print_warning "KVM device not available. Will try to install KVM packages."
    fi
}

install_dependencies() {
    print_header "Installing Dependencies"
    
    # Detect OS
    if [[ -f /etc/redhat-release ]]; then
        OS="rhel"
        PKG_MGR="dnf"
    elif [[ -f /etc/debian_version ]]; then
        OS="debian"
        PKG_MGR="apt"
    else
        print_error "Unsupported OS. This script supports RHEL/CentOS/Fedora and Debian/Ubuntu."
        exit 1
    fi
    
    print_info "Detected OS: $OS"
    
    if [[ $OS == "rhel" ]]; then
        sudo $PKG_MGR install -y qemu-kvm libvirt virt-install virt-manager virt-builder bridge-utils python3-libvirt python3-lxml ansible git wget curl
    else
        sudo apt update
        sudo $PKG_MGR install -y qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils virt-manager virt-builder python3-libvirt ansible git wget curl
    fi
    
    # Start and enable libvirtd
    sudo systemctl enable --now libvirtd
    
    # Add user to libvirt group
    sudo usermod -aG libvirt $USER
    
    print_success "Dependencies installed successfully."
}

setup_infrastructure() {
    print_header "Setting Up Infrastructure"
    
    # Check if ansible playbook exists
    if [[ ! -f "ansible-okd-automation.yml" ]]; then
        print_error "ansible-okd-automation.yml not found. Please ensure all files are in current directory."
        exit 1
    fi
    
    print_info "Running infrastructure setup with Ansible..."
    ansible-playbook ansible-okd-automation.yml
    
    print_success "Infrastructure setup completed."
}

configure_bastion() {
    print_header "Configuring Bastion VM"
    
    print_info "Waiting for bastion VM to be ready..."
    sleep 30
    
    print_warning "Manual step required: Configure network in bastion VM"
    echo
    echo "Please run the following commands in bastion VM:"
    echo "1. Connect to bastion VM:"
    echo "   sudo virsh console $BASTION_NAME"
    echo
    echo "2. Configure network (login: root / password: rahasia):"
    echo "   nmcli con add type ethernet con-name enp1s0 ifname enp1s0 \\"
    echo "     connection.autoconnect yes ipv4.method manual \\"
    echo "     ipv4.address $BASTION_IP/24 ipv4.gateway ************* \\"
    echo "     ipv4.dns *******"
    echo
    echo "3. Test connectivity:"
    echo "   ping -c 2 *******"
    echo
    echo "4. Update system:"
    echo "   dnf -y upgrade && reboot"
    echo
    
    read -p "Press Enter after completing bastion network configuration..."
    
    print_info "Testing bastion connectivity..."
    if ping -c 2 $BASTION_IP >/dev/null 2>&1; then
        print_success "Bastion VM is reachable at $BASTION_IP"
    else
        print_warning "Cannot reach bastion VM. Please verify network configuration."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

setup_bastion_services() {
    print_header "Setting Up Bastion Services"
    
    print_warning "Manual step required: Run bastion setup in bastion VM"
    echo
    echo "Please run the following commands in bastion VM:"
    echo "1. Connect to bastion VM:"
    echo "   sudo virsh console $BASTION_NAME"
    echo
    echo "2. Download and run bastion setup:"
    echo "   wget https://raw.githubusercontent.com/your-repo/ansible-bastion-setup.yml"
    echo "   ansible-playbook ansible-bastion-setup.yml"
    echo
    echo "   OR copy the ansible-bastion-setup.yml file to bastion VM and run it."
    echo
    
    read -p "Press Enter after completing bastion services setup..."
    
    print_info "Testing bastion services..."
    
    # Test HTTP service
    if curl -s -I http://$BASTION_IP:8080 >/dev/null 2>&1; then
        print_success "HTTP service is running on bastion"
    else
        print_warning "HTTP service not accessible on bastion"
    fi
    
    # Test DNS service
    if nslookup bootstrap.$CLUSTER_NAME.$CLUSTER_DOMAIN $BASTION_IP >/dev/null 2>&1; then
        print_success "DNS service is working on bastion"
    else
        print_warning "DNS service not working on bastion"
    fi
}

generate_ignition_files() {
    print_header "Generating Ignition Files"
    
    print_warning "Manual step required: Generate ignition files in bastion VM"
    echo
    echo "Please run the following commands in bastion VM:"
    echo
    echo "1. Generate SSH key:"
    echo "   ssh-keygen -t rsa -N \"\" -f ~/.ssh/id_rsa"
    echo
    echo "2. Create install config:"
    echo "   mkdir -p ~/okd4"
    echo "   cat <<EOF > ~/install-config-okd.yaml"
    echo "apiVersion: v1"
    echo "baseDomain: $CLUSTER_DOMAIN"
    echo "compute:"
    echo "- hyperthreading: Enabled"
    echo "  name: worker"
    echo "  replicas: 0"
    echo "controlPlane:"
    echo "  hyperthreading: Enabled"
    echo "  name: master"
    echo "  replicas: 3"
    echo "metadata:"
    echo "  name: $CLUSTER_NAME"
    echo "networking:"
    echo "  clusterNetworks:"
    echo "  - cidr: **********/14"
    echo "    hostPrefix: 23"
    echo "  networkType: OVNKubernetes"
    echo "  serviceNetwork:"
    echo "  - **********/16"
    echo "platform:"
    echo "  none: {}"
    echo "fips: false"
    echo "sshKey: '\$(< ~/.ssh/id_rsa.pub)'"
    echo "EOF"
    echo
    echo "3. Generate ignition files:"
    echo "   cp install-config-okd.yaml okd4/install-config.yaml"
    echo "   cd okd4"
    echo "   openshift-install create manifests"
    echo "   sed -i 's/true/false/' manifests/cluster-scheduler-02-config.yml"
    echo "   openshift-install create ignition-configs"
    echo
    echo "4. Copy to web server:"
    echo "   sudo cp -v *.ign /var/www/html/ignition/"
    echo "   sudo chmod 644 /var/www/html/ignition/*.ign"
    echo "   sudo restorecon -RFv /var/www/html/"
    echo
    
    read -p "Press Enter after generating ignition files..."
    
    # Test ignition files accessibility
    if curl -s -I http://$BASTION_IP:8080/ignition/bootstrap.ign >/dev/null 2>&1; then
        print_success "Ignition files are accessible via HTTP"
    else
        print_warning "Ignition files not accessible. Please verify the setup."
    fi
}

start_cluster_installation() {
    print_header "Starting Cluster Installation"
    
    print_info "Starting bootstrap VM..."
    sudo virsh start bootstrap.$CLUSTER_NAME.$CLUSTER_DOMAIN 2>/dev/null || print_warning "Bootstrap VM might already be running"
    
    print_warning "Manual step required: Monitor and complete installation in bastion VM"
    echo
    echo "Please run the following commands in bastion VM:"
    echo
    echo "1. Monitor bootstrap process:"
    echo "   cd ~/okd4"
    echo "   openshift-install --dir=. wait-for bootstrap-complete --log-level=info"
    echo
    echo "2. Start master VMs after bootstrap completes:"
    echo "   sudo virsh start master01.$CLUSTER_NAME.$CLUSTER_DOMAIN"
    echo "   sudo virsh start master02.$CLUSTER_NAME.$CLUSTER_DOMAIN"
    echo "   sudo virsh start master03.$CLUSTER_NAME.$CLUSTER_DOMAIN"
    echo
    echo "3. Approve CSRs and complete installation:"
    echo "   export KUBECONFIG=~/okd4/auth/kubeconfig"
    echo "   oc get csr -o go-template='{{range .items}}{{if not .status}}{{.metadata.name}}{{\"\\n\"}}{{end}}{{end}}' | xargs oc adm certificate approve"
    echo "   openshift-install --dir=. wait-for install-complete --log-level=info"
    echo
    echo "4. Start worker VMs (optional):"
    echo "   sudo virsh start worker01.$CLUSTER_NAME.$CLUSTER_DOMAIN"
    echo "   sudo virsh start worker02.$CLUSTER_NAME.$CLUSTER_DOMAIN"
    echo "   sudo virsh start worker03.$CLUSTER_NAME.$CLUSTER_DOMAIN"
    echo
    
    read -p "Press Enter after completing cluster installation..."
}

display_completion_info() {
    print_header "Installation Complete!"
    
    print_success "OKD 4.19 cluster installation process completed!"
    echo
    print_info "Cluster Information:"
    echo "  - Cluster Name: $CLUSTER_NAME"
    echo "  - Domain: $CLUSTER_DOMAIN"
    echo "  - Bastion IP: $BASTION_IP"
    echo "  - Console URL: https://console-openshift-console.apps.$CLUSTER_NAME.$CLUSTER_DOMAIN"
    echo
    print_info "To access your cluster:"
    echo "  1. Connect to bastion VM: sudo virsh console $BASTION_NAME"
    echo "  2. Export kubeconfig: export KUBECONFIG=~/okd4/auth/kubeconfig"
    echo "  3. Check cluster: oc get nodes"
    echo "  4. Get admin password: cat ~/okd4/auth/kubeadmin-password"
    echo
    print_info "Useful commands:"
    echo "  - List VMs: sudo virsh list --all"
    echo "  - VM console: sudo virsh console <vm-name>"
    echo "  - Check services: systemctl status dhcpd named tftp httpd haproxy"
    echo
    print_success "Happy clustering! 🚀"
}

# Main execution
main() {
    print_header "OKD 4.19 Complete Installation Script"
    echo "This script will guide you through the complete OKD 4.19 installation process."
    echo
    
    read -p "Do you want to proceed? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Installation cancelled."
        exit 0
    fi
    
    check_requirements
    install_dependencies
    setup_infrastructure
    configure_bastion
    setup_bastion_services
    generate_ignition_files
    start_cluster_installation
    display_completion_info
}

# Run main function
main "$@"
