<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tutorial Lengkap: Install OKD 4.19 dengan Fedora CoreOS di KVM (v2)</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #d73027;
            border-bottom: 3px solid #d73027;
            padding-bottom: 10px;
            text-align: center;
        }
        h2 {
            color: #2166ac;
            border-left: 4px solid #2166ac;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #5aae61;
            margin-top: 25px;
        }
        .changelog {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .changelog h2 {
            margin-top: 0;
            color: #2e7d32;
            border: none;
            padding: 0;
        }
        .changelog ul {
            margin: 10px 0;
        }
        .changelog li {
            margin: 5px 0;
        }
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .toc h2 {
            margin-top: 0;
            color: #495057;
            border: none;
            padding: 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #007bff;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 3px;
            transition: background-color 0.3s;
        }
        .toc a:hover {
            background-color: #e9ecef;
        }
        pre {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
            border-left: 4px solid #4299e1;
        }
        code {
            background-color: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            color: #d73027;
        }
        pre code {
            background-color: transparent;
            padding: 0;
            color: #e2e8f0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-left: 4px solid #f39c12;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 4px solid #17a2b8;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .new-feature {
            background-color: #fff3e0;
            border: 1px solid #ffcc02;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .step-number {
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .command-block {
            position: relative;
        }
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .copy-btn:hover {
            background-color: #5a6268;
        }
        .nav-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 50px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            z-index: 1000;
        }
        .nav-top:hover {
            background-color: #0056b3;
        }
        .version-badge {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tutorial Lengkap: Install OKD 4.19 dengan Fedora CoreOS di KVM <span class="version-badge">v2</span></h1>
        
        <div class="changelog">
            <h2>🆕 Changelog v2</h2>
            <ul>
                <li>✅ <strong>Updated virt-install parameters</strong> dengan ignition file support sesuai Fedora CoreOS docs</li>
                <li>✅ <strong>Menggunakan <code>--qemu-commandline</code></strong> untuk pass ignition config ke FCOS VMs</li>
                <li>✅ <strong>Proper FCOS image handling</strong> dengan backing store</li>
                <li>✅ <strong>SELinux labeling</strong> untuk ignition files</li>
                <li>✅ <strong>OS variant</strong> yang lebih tepat untuk FCOS</li>
                <li>✅ <strong>Import mode</strong> untuk menggunakan FCOS image yang sudah ada</li>
                <li>✅ <strong>No PXE dependency</strong> untuk cluster VMs (direct ignition)</li>
            </ul>
        </div>

        <div class="info">
            <strong>Info:</strong> Tutorial v2 ini menggunakan pendekatan yang lebih modern dengan direct ignition file injection ke FCOS VMs, menghilangkan ketergantungan pada PXE boot untuk cluster nodes.
        </div>

        <div class="toc">
            <h2>Daftar Isi</h2>
            <ul>
                <li><a href="#persiapan">1. Persiapan Environment</a></li>
                <li><a href="#kvm-setup">2. Setup KVM Infrastructure</a></li>
                <li><a href="#bastion-vm">3. Buat Bastion VM</a></li>
                <li><a href="#ansible">4. Install Ansible dan Konfigurasi</a></li>
                <li><a href="#dhcp">5. Setup DHCP Server</a></li>
                <li><a href="#dns">6. Setup DNS Server</a></li>
                <li><a href="#haproxy">7. Setup HAProxy Load Balancer</a></li>
                <li><a href="#okd-installer">8. Download OKD Installer dan CLI</a></li>
                <li><a href="#fcos-images">9. Download FCOS Images</a></li>
                <li><a href="#ignition">10. Generate Ignition Files</a></li>
                <li><a href="#cluster-vms">11. Buat Cluster VMs dengan Ignition</a></li>
                <li><a href="#install">12. Install OKD Cluster</a></li>
                <li><a href="#troubleshooting">13. Troubleshooting</a></li>
            </ul>
        </div>

        <h2 id="specs">Spesifikasi Environment</h2>
        
        <h3>Network Configuration</h3>
        <ul>
            <li><strong>Network Range:</strong> *************/24</li>
            <li><strong>Gateway:</strong> *************</li>
            <li><strong>Bastion IP:</strong> ***************</li>
            <li><strong>Domain:</strong> febryan.web.id.example</li>
            <li><strong>Cluster Name:</strong> okd4</li>
        </ul>

        <h3>VM Requirements</h3>
        <table>
            <thead>
                <tr>
                    <th>VM Type</th>
                    <th>vCPU</th>
                    <th>RAM</th>
                    <th>Storage</th>
                    <th>IP Address</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Bastion</td>
                    <td>2</td>
                    <td>4GB</td>
                    <td>20GB</td>
                    <td>***************</td>
                </tr>
                <tr>
                    <td>Bootstrap</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>*************0</td>
                </tr>
                <tr>
                    <td>Master01</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>*************1</td>
                </tr>
                <tr>
                    <td>Master02</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>**************</td>
                </tr>
                <tr>
                    <td>Master03</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>*************3</td>
                </tr>
                <tr>
                    <td>Worker01</td>
                    <td>2</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>192.168.101.21</td>
                </tr>
                <tr>
                    <td>Worker02</td>
                    <td>2</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>192.168.101.22</td>
                </tr>
                <tr>
                    <td>Worker03</td>
                    <td>2</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>**************</td>
                </tr>
            </tbody>
        </table>

        <h2 id="persiapan">1. Persiapan Environment</h2>
        
        <h3><span class="step-number">1</span>Verifikasi CPU Virtualization Support</h3>
        <div class="command-block">
            <pre><code>cat /proc/cpuinfo | egrep "vmx|svm"</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">2</span>Install KVM Packages</h3>
        <div class="command-block">
            <pre><code># Ubuntu/Debian
sudo apt update
sudo apt install -y qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils virt-manager virt-builder

# RHEL/CentOS/Fedora
sudo dnf install -y qemu-kvm libvirt virt-install virt-manager virt-builder

# Start dan enable libvirt
sudo systemctl enable --now libvirtd
sudo usermod -aG libvirt $USER</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="fcos-images">9. Download FCOS Images</h2>
        
        <div class="new-feature">
            <strong>🆕 New in v2:</strong> Direct FCOS image download dan usage dengan proper ignition integration
        </div>

        <h3><span class="step-number">1</span>Download FCOS Images untuk Direct Boot</h3>
        <div class="command-block">
            <pre><code># Buat direktori untuk FCOS images
mkdir -p ~/.local/share/libvirt/images/

# Download FCOS image menggunakan coreos-installer
STREAM="stable"

# Install coreos-installer jika belum ada
sudo dnf install -y coreos-installer

# Download FCOS qcow2 image
coreos-installer download -s $STREAM -p qemu -f qcow2.xz --decompress -C ~/.local/share/libvirt/images/

# Atau download manual (jika coreos-installer tidak tersedia)
cd ~/.local/share/libvirt/images/
wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-qemu.x86_64.qcow2.xz
xz -d fedora-coreos-42.20250705.3.0-qemu.x86_64.qcow2.xz

# Rename untuk kemudahan
mv fedora-coreos-*.qcow2 fedora-coreos-stable.qcow2

# Set SELinux context
sudo chcon -t svirt_home_t ~/.local/share/libvirt/images/fedora-coreos-stable.qcow2</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="cluster-vms">11. Buat Cluster VMs dengan Ignition</h2>
        
        <div class="new-feature">
            <strong>🆕 New in v2:</strong> VM creation dengan direct ignition file injection menggunakan qemu-commandline
        </div>

        <h3><span class="step-number">1</span>Setup Variables untuk VM Creation</h3>
        <div class="command-block">
            <pre><code># Set variables
FCOS_IMAGE="$HOME/.local/share/libvirt/images/fedora-coreos-stable.qcow2"
IGNITION_DIR="$HOME/okd4"
NETWORK_NAME="openshift4"

# Verifikasi files exist
ls -la $FCOS_IMAGE
ls -la $IGNITION_DIR/*.ign</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">2</span>Buat Bootstrap VM dengan Ignition</h3>
        <div class="command-block">
            <pre><code># Bootstrap VM dengan ignition
IGNITION_CONFIG="$IGNITION_DIR/bootstrap.ign"
VM_NAME="bootstrap.okd4.febryan.web.id.example"
VCPUS="4"
RAM_MB="8192"
DISK_GB="50"

# Set SELinux context
sudo chcon -t svirt_home_t ${IGNITION_CONFIG}

virt-install --connect="qemu:///system" \
  --name="${VM_NAME}" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:a4:db:5f \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <div class="info">
            <strong>Key Parameters Explained:</strong>
            <ul>
                <li><code>--import</code>: Use existing FCOS image instead of installation</li>
                <li><code>backing_store</code>: Use FCOS image as backing store for efficiency</li>
                <li><code>--qemu-commandline</code>: Pass ignition file directly to FCOS</li>
                <li><code>--noautoconsole</code>: Don't automatically connect to console</li>
            </ul>
        </div>

    </div>

    <button class="nav-top" onclick="scrollToTop()">↑ Top</button>

    <script>
        function copyToClipboard(button) {
            const codeBlock = button.previousElementSibling;
            const text = codeBlock.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                button.textContent = 'Copied!';
                button.style.backgroundColor = '#28a745';
                setTimeout(function() {
                    button.textContent = 'Copy';
                    button.style.backgroundColor = '#6c757d';
                }, 2000);
            });
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
