---
# Ansible Playbook untuk Setup Services di Bastion VM
# Jalankan playbook ini di dalam Bastion VM setelah network dikonfigurasi

- name: Setup OKD Bastion Services
  hosts: localhost
  become: yes
  vars:
    # Network Configuration
    bastion_ip: "***************"
    network_gateway: "*************"
    network_interface: "enp1s0"
    
    # Domain Configuration
    cluster_domain: "febryan.web.id.example"
    cluster_name: "okd4"
    
    # FCOS Images URLs (Latest)
    fcos_kernel_url: "https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-live-kernel.x86_64"
    fcos_initramfs_url: "https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-live-initramfs.x86_64.img"
    fcos_rootfs_url: "https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-live-rootfs.x86_64.img"
    
    # OKD Installer URLs (Latest)
    okd_client_url: "https://github.com/okd-project/okd/releases/download/4.19.0-okd-scos.10/openshift-client-linux-4.19.0-okd-scos.10.tar.gz"
    okd_installer_url: "https://github.com/okd-project/okd/releases/download/4.19.0-okd-scos.10/openshift-install-linux-4.19.0-okd-scos.10.tar.gz"

  tasks:
    # ========================================
    # 1. Install Required Packages
    # ========================================
    - name: Update system packages
      dnf:
        name: "*"
        state: latest

    - name: Install base packages
      dnf:
        name:
          - git
          - ansible
          - vim
          - wget
          - curl
          - bash-completion
          - tree
          - tar
          - libselinux-python3
          - firewalld
          - bind
          - bind-utils
          - dhcp-server
          - tftp-server
          - syslinux
          - haproxy
          - httpd
          - policycoreutils-python-utils
        state: present

    - name: Start and enable firewalld
      systemd:
        name: firewalld
        state: started
        enabled: yes

    # ========================================
    # 2. Clone OCP4 Ansible Repository
    # ========================================
    - name: Clone ocp4_ansible repository
      git:
        repo: https://github.com/jmutai/ocp4_ansible.git
        dest: /root/ocp4_ansible
        force: yes

    - name: Create ansible configuration
      copy:
        content: |
          [defaults]
          inventory = inventory
          command_warnings = False
          host_key_checking = False
          deprecation_warnings = False
          retry_files = false

          [privilege_escalation]
          become = true
          become_method = sudo
          become_user = root
          become_ask_pass = false
        dest: /root/ocp4_ansible/ansible.cfg

    - name: Create inventory file
      copy:
        content: |
          [vms_host]
          localhost ansible_connection=local
        dest: /root/ocp4_ansible/inventory

    - name: Update variables for OKD
      copy:
        content: |
          ---
          ppc64le: false
          uefi: false
          disk: vda
          helper:
            name: "bastion"
            ipaddr: "{{ bastion_ip }}"
            networkifacename: "{{ network_interface }}"
          dns:
            domain: "{{ cluster_domain }}"
            clusterid: "{{ cluster_name }}"
            forwarder1: "*******"
            forwarder2: "*******"
            lb_ipaddr: "{{ bastion_ip }}"
          dhcp:
            router: "{{ network_gateway }}"
            bcast: "***************"
            netmask: "*************"
            poolstart: "*************0"
            poolend: "**************"
            ipid: "*************"
            netmaskid: "*************"
            ntp: "time.google.com"
            dns: ""
          bootstrap:
            name: "bootstrap"
            ipaddr: "*************0"
            macaddr: "52:54:00:a4:db:5f"
          masters:
            - name: "master01"
              ipaddr: "*************1"
              macaddr: "52:54:00:8b:a1:17"
            - name: "master02"
              ipaddr: "*************2"
              macaddr: "52:54:00:ea:8b:9d"
            - name: "master03"
              ipaddr: "*************3"
              macaddr: "52:54:00:f8:87:c7"
          workers:
            - name: "worker01"
              ipaddr: "**************"
              macaddr: "52:54:00:31:4a:39"
            - name: "worker02"
              ipaddr: "**************"
              macaddr: "52:54:00:6a:37:32"
            - name: "worker03"
              ipaddr: "**************"
              macaddr: "52:54:00:95:d4:ed"
        dest: /root/ocp4_ansible/vars/main.yml

    # ========================================
    # 3. Update PXE Templates for FCOS
    # ========================================
    - name: Update PXE bootstrap template for FCOS
      copy:
        content: |
          {% if bootstrap.ipaddr is defined and bootstrap.networkifacename is defined %}
            {% set ipconfig = bootstrap.ipaddr + "::" + dhcp.router + ":" + dhcp.netmask + ":" + bootstrap.name + ":" + bootstrap.networkifacename + ":none" %}
            {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder1 %}
            {% if dns.forwarder2 is defined %}
              {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder2 %}
            {% endif %}
          {% else %}
            {% set ipconfig = "dhcp" %}
          {% endif %}

          default menu.c32   
           prompt 1
           timeout 9
           ONTIMEOUT 1   
           menu title ######## PXE Boot Menu - OKD Bootstrap ########  
           label 1  
           menu label ^1) Install OKD Bootstrap Node (FCOS)
           menu default
           kernel fcos/kernel
           append initrd=fcos/initramfs.img nomodeset rd.neednet=1 console=tty0 console=ttyS0 ip={{ ipconfig }} coreos.inst=yes coreos.inst.install_dev={{ disk }} coreos.live.rootfs_url=http://{{ helper.ipaddr }}:8080/fcos/rootfs.img coreos.inst.ignition_url=http://{{ helper.ipaddr }}:8080/ignition/bootstrap.ign
        dest: /root/ocp4_ansible/templates/pxe-bootstrap.j2

    - name: Update PXE master template for FCOS
      copy:
        content: |
          {% if item.ipaddr is defined and item.networkifacename is defined %}
            {% set ipconfig = item.ipaddr + "::" + dhcp.router + ":" + dhcp.netmask + ":" + item.name + ":" + item.networkifacename + ":none" %}
            {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder1 %}
            {% if dns.forwarder2 is defined %}
              {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder2 %}
            {% endif %}
          {% else %}
            {% set ipconfig = "dhcp" %}
          {% endif %}

          default menu.c32
           prompt 1
           timeout 9
           ONTIMEOUT 1
           menu title ######## PXE Boot Menu - OKD Master ########
           label 1
           menu label ^1) Install OKD Master Node (FCOS)
           menu default
           kernel fcos/kernel
           append initrd=fcos/initramfs.img nomodeset rd.neednet=1 console=tty0 console=ttyS0 ip={{ ipconfig }} coreos.inst=yes coreos.inst.install_dev={{ disk }} coreos.live.rootfs_url=http://{{ helper.ipaddr }}:8080/fcos/rootfs.img coreos.inst.ignition_url=http://{{ helper.ipaddr }}:8080/ignition/master.ign
        dest: /root/ocp4_ansible/templates/pxe-master.j2

    - name: Update PXE worker template for FCOS
      copy:
        content: |
          {% if item.ipaddr is defined and item.networkifacename is defined %}
            {% set ipconfig = item.ipaddr + "::" + dhcp.router + ":" + dhcp.netmask + ":" + item.name + ":" + item.networkifacename + ":none" %}
            {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder1 %}
            {% if dns.forwarder2 is defined %}
              {% set ipconfig = ipconfig + " nameserver=" + dns.forwarder2 %}
            {% endif %}
          {% else %}
            {% set ipconfig = "dhcp" %}
          {% endif %}

          default menu.c32
           prompt 1
           timeout 9
           ONTIMEOUT 1
           menu title ######## PXE Boot Menu - OKD Worker ########
           label 1
           menu label ^1) Install OKD Worker Node (FCOS)
           menu default
           kernel fcos/kernel
           append initrd=fcos/initramfs.img nomodeset rd.neednet=1 console=tty0 console=ttyS0 ip={{ ipconfig }} coreos.inst=yes coreos.inst.install_dev={{ disk }} coreos.live.rootfs_url=http://{{ helper.ipaddr }}:8080/fcos/rootfs.img coreos.inst.ignition_url=http://{{ helper.ipaddr }}:8080/ignition/worker.ign
        dest: /root/ocp4_ansible/templates/pxe-worker.j2

    # ========================================
    # 4. Setup DHCP Server
    # ========================================
    - name: Enable DHCP service
      systemd:
        name: dhcpd
        enabled: yes

    - name: Backup original DHCP config
      copy:
        src: /etc/dhcp/dhcpd.conf
        dest: /etc/dhcp/dhcpd.conf.bak
        remote_src: yes
      ignore_errors: yes

    - name: Configure DHCP with Ansible
      command: ansible-playbook tasks/configure_dhcpd.yml
      args:
        chdir: /root/ocp4_ansible

    # ========================================
    # 5. Setup DNS Server
    # ========================================
    - name: Enable DNS service
      systemd:
        name: named
        enabled: yes

    - name: Create DNS serial script
      copy:
        content: |
          #!/bin/bash
          dnsserialfile=/usr/local/src/dnsserial-DO_NOT_DELETE_BEFORE_ASKING_CHRISTIAN.txt
          zonefile=/var/named/zonefile.db
          if [ -f zonefile ] ; then
              echo $[ $(grep serial ${zonefile} | tr -d "\t"" ""\n" | cut -d';' -f 1) + 1 ] | tee ${dnsserialfile}
          else
              if [ ! -f ${dnsserialfile} ] || [ ! -s ${dnsserialfile} ]; then
                  echo $(date +%Y%m%d00) | tee ${dnsserialfile}
              else
                  echo $[ $(< ${dnsserialfile}) + 1 ] | tee ${dnsserialfile}
              fi
          fi
        dest: /usr/local/bin/set-dns-serial.sh
        mode: '0755'

    - name: Configure DNS with Ansible
      command: ansible-playbook tasks/configure_bind_dns.yml
      args:
        chdir: /root/ocp4_ansible

    - name: Update network to use local DNS
      command: nmcli connection modify {{ network_interface }} ipv4.dns "{{ bastion_ip }}"

    - name: Reload network connection
      command: nmcli connection reload

    - name: Restart network connection
      command: nmcli connection up {{ network_interface }}

    # ========================================
    # 6. Setup TFTP/PXE Server
    # ========================================
    - name: Create TFTP systemd service
      copy:
        content: |
          [Unit]
          Description=Starts TFTP on boot because of reasons
          After=network.target

          [Service]
          Type=simple
          ExecStart=/usr/local/bin/start-tftp.sh
          TimeoutStartSec=0
          Restart=always
          RestartSec=30

          [Install]
          WantedBy=default.target
        dest: /etc/systemd/system/helper-tftp.service

    - name: Create TFTP helper script
      copy:
        content: |
          #!/bin/bash
          /usr/bin/systemctl start tftp > /dev/null 2>&1
        dest: /usr/local/bin/start-tftp.sh
        mode: '0755'

    - name: Reload systemd daemon
      systemd:
        daemon_reload: yes

    - name: Enable and start TFTP services
      systemd:
        name: "{{ item }}"
        enabled: yes
        state: started
      loop:
        - tftp
        - helper-tftp

    - name: Create TFTP directories
      file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - /var/lib/tftpboot/pxelinux.cfg
        - /var/lib/tftpboot/fcos

    - name: Copy syslinux files
      command: cp -rvf /usr/share/syslinux/* /var/lib/tftpboot

    # ========================================
    # 7. Download FCOS Images
    # ========================================
    - name: Create FCOS download directory
      file:
        path: /root/fcos-images
        state: directory

    - name: Download FCOS kernel
      get_url:
        url: "{{ fcos_kernel_url }}"
        dest: /root/fcos-images/kernel
        timeout: 300

    - name: Download FCOS initramfs
      get_url:
        url: "{{ fcos_initramfs_url }}"
        dest: /root/fcos-images/initramfs.img
        timeout: 300

    - name: Download FCOS rootfs
      get_url:
        url: "{{ fcos_rootfs_url }}"
        dest: /root/fcos-images/rootfs.img
        timeout: 600

    - name: Move FCOS files to TFTP location
      copy:
        src: "{{ item.src }}"
        dest: "{{ item.dest }}"
        remote_src: yes
        mode: '0644'
      loop:
        - { src: "/root/fcos-images/kernel", dest: "/var/lib/tftpboot/fcos/kernel" }
        - { src: "/root/fcos-images/initramfs.img", dest: "/var/lib/tftpboot/fcos/initramfs.img" }

    # ========================================
    # 8. Setup HTTP Server
    # ========================================
    - name: Configure httpd for port 8080
      lineinfile:
        path: /etc/httpd/conf/httpd.conf
        regexp: '^Listen 80'
        line: 'Listen 8080'

    - name: Remove httpd welcome page
      file:
        path: /etc/httpd/conf.d/welcome.conf
        state: absent

    - name: Create web directories
      file:
        path: "{{ item }}"
        state: directory
        mode: '0755'
      loop:
        - /var/www/html/fcos
        - /var/www/html/ignition

    - name: Move FCOS rootfs to web server
      copy:
        src: /root/fcos-images/rootfs.img
        dest: /var/www/html/fcos/rootfs.img
        remote_src: yes
        mode: '0644'

    - name: Enable and start httpd
      systemd:
        name: httpd
        enabled: yes
        state: started

    - name: Restore SELinux contexts
      command: restorecon -RFv {{ item }}
      loop:
        - /var/lib/tftpboot/fcos
        - /var/www/html/fcos

    # ========================================
    # 9. Setup HAProxy
    # ========================================
    - name: Set HAProxy SELinux boolean
      seboolean:
        name: haproxy_connect_any
        state: yes
        persistent: yes

    - name: Backup original HAProxy config
      copy:
        src: /etc/haproxy/haproxy.cfg
        dest: /etc/haproxy/haproxy.cfg.default
        remote_src: yes

    - name: Configure HAProxy with Ansible
      command: ansible-playbook tasks/configure_haproxy_lb.yml
      args:
        chdir: /root/ocp4_ansible

    - name: Configure SELinux ports for HAProxy
      command: semanage port -a {{ item }} -t http_port_t -p tcp
      loop:
        - 6443
        - 22623
        - 32700
      ignore_errors: yes

    - name: Enable and start HAProxy
      systemd:
        name: haproxy
        enabled: yes
        state: started

    # ========================================
    # 10. Configure Firewall
    # ========================================
    - name: Open firewall services
      firewalld:
        service: "{{ item }}"
        permanent: yes
        state: enabled
        immediate: yes
      loop:
        - dhcp
        - tftp
        - http
        - https
        - dns

    - name: Open firewall ports
      firewalld:
        port: "{{ item }}"
        permanent: yes
        state: enabled
        immediate: yes
      loop:
        - 8080/tcp
        - 6443/tcp
        - 22623/tcp

    # ========================================
    # 11. Generate PXE Files
    # ========================================
    - name: Generate PXE files with Ansible
      command: ansible-playbook tasks/configure_tftp_pxe.yml
      args:
        chdir: /root/ocp4_ansible

    # ========================================
    # 12. Download OKD Installer
    # ========================================
    - name: Download OKD client
      get_url:
        url: "{{ okd_client_url }}"
        dest: /root/okd-client.tar.gz
        timeout: 300

    - name: Download OKD installer
      get_url:
        url: "{{ okd_installer_url }}"
        dest: /root/okd-installer.tar.gz
        timeout: 300

    - name: Extract OKD client
      unarchive:
        src: /root/okd-client.tar.gz
        dest: /root
        remote_src: yes

    - name: Extract OKD installer
      unarchive:
        src: /root/okd-installer.tar.gz
        dest: /root
        remote_src: yes

    - name: Install OKD binaries
      copy:
        src: "/root/{{ item }}"
        dest: "/usr/local/bin/{{ item }}"
        remote_src: yes
        mode: '0755'
      loop:
        - oc
        - kubectl
        - openshift-install

    - name: Cleanup downloaded files
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /root/okd-client.tar.gz
        - /root/okd-installer.tar.gz
        - /root/README.md
        - /root/LICENSE

    # ========================================
    # 13. Final Status Check
    # ========================================
    - name: Check all services status
      command: systemctl status {{ item }}
      loop:
        - dhcpd
        - named
        - tftp
        - httpd
        - haproxy
      register: service_status
      ignore_errors: yes

    - name: Display setup completion message
      debug:
        msg: |
          ========================================
          OKD Bastion Setup Complete!
          ========================================
          
          Services Status:
          {% for result in service_status.results %}
          - {{ result.item }}: {{ 'Running' if result.rc == 0 else 'Failed' }}
          {% endfor %}
          
          Next Steps:
          1. Generate SSH key: ssh-keygen -t rsa -N "" -f ~/.ssh/id_rsa
          2. Create install-config.yaml for OKD
          3. Generate ignition files
          4. Start cluster VMs for installation
          
          Useful Commands:
          - Test DNS: dig @127.0.0.1 -t srv _etcd-server-ssl._tcp.{{ cluster_name }}.{{ cluster_domain }}
          - Test HTTP: curl -I http://{{ bastion_ip }}:8080/fcos/rootfs.img
          - Check PXE files: ls -la /var/lib/tftpboot/pxelinux.cfg/
          
          OKD Installer Version:
          $(openshift-install version)
          
          Ready for cluster installation!
