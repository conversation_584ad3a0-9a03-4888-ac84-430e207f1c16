<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tutorial Lengkap: Install OKD 4.19 dengan Fedora CoreOS di KVM (v3)</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #d73027;
            border-bottom: 3px solid #d73027;
            padding-bottom: 15px;
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
        }
        h2 {
            color: #2166ac;
            border-left: 5px solid #2166ac;
            padding-left: 20px;
            margin-top: 40px;
            font-size: 1.8em;
        }
        h3 {
            color: #5aae61;
            margin-top: 30px;
            font-size: 1.4em;
        }
        h4 {
            color: #f39c12;
            margin-top: 25px;
            font-size: 1.2em;
        }
        .version-badge {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: bold;
            margin-left: 15px;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }
        .changelog {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            border: 1px solid #4caf50;
            border-left: 5px solid #4caf50;
            padding: 20px;
            margin: 25px 0;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(76, 175, 80, 0.1);
        }
        .changelog h2 {
            margin-top: 0;
            color: #2e7d32;
            border: none;
            padding: 0;
        }
        .changelog ul {
            margin: 15px 0;
        }
        .changelog li {
            margin: 8px 0;
            padding-left: 10px;
        }
        .config-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin: 25px 0;
        }
        .config-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .config-card h4 {
            margin-top: 0;
            color: #495057;
            text-align: center;
            font-size: 1.3em;
        }
        .standard-cluster {
            border-left: 5px solid #007bff;
        }
        .compact-cluster {
            border-left: 5px solid #ff9800;
        }
        .toc {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .toc h2 {
            margin-top: 0;
            color: #495057;
            border: none;
            padding: 0;
            text-align: center;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
            columns: 2;
            column-gap: 30px;
        }
        .toc li {
            margin: 10px 0;
            break-inside: avoid;
        }
        .toc a {
            color: #007bff;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 5px;
            transition: all 0.3s ease;
            display: block;
        }
        .toc a:hover {
            background-color: #007bff;
            color: white;
            transform: translateX(5px);
        }
        pre {
            background: linear-gradient(135deg, #2d3748, #1a202c);
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 20px 0;
            border-left: 5px solid #4299e1;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        code {
            background-color: #f1f3f4;
            padding: 3px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #d73027;
            font-weight: 500;
        }
        pre code {
            background-color: transparent;
            padding: 0;
            color: #e2e8f0;
            font-weight: normal;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 15px;
            text-align: left;
        }
        th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            font-weight: bold;
            color: #495057;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #e3f2fd;
            transition: background-color 0.3s ease;
        }
        .warning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border: 1px solid #ffeaa7;
            border-left: 5px solid #f39c12;
            padding: 20px;
            margin: 25px 0;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.1);
        }
        .info {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            border: 1px solid #bee5eb;
            border-left: 5px solid #17a2b8;
            padding: 20px;
            margin: 25px 0;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(23, 162, 184, 0.1);
        }
        .success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 1px solid #c3e6cb;
            border-left: 5px solid #28a745;
            padding: 20px;
            margin: 25px 0;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.1);
        }
        .step-number {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }
        .command-block {
            position: relative;
        }
        .copy-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(45deg, #6c757d, #5a6268);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        .copy-btn:hover {
            background: linear-gradient(45deg, #5a6268, #495057);
            transform: scale(1.05);
        }
        .nav-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 50px;
            cursor: pointer;
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .nav-top:hover {
            background: linear-gradient(45deg, #0056b3, #004085);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.5);
        }
        .resource-summary {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            border: 1px solid #4caf50;
            border-left: 5px solid #4caf50;
            padding: 20px;
            margin: 25px 0;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 4px 8px rgba(76, 175, 80, 0.1);
        }
        .progress-indicator {
            background: linear-gradient(90deg, #007bff, #28a745);
            height: 4px;
            border-radius: 2px;
            margin: 20px 0;
        }
        @media (max-width: 768px) {
            .config-comparison {
                grid-template-columns: 1fr;
            }
            .toc ul {
                columns: 1;
            }
            .container {
                padding: 20px;
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tutorial Lengkap: Install OKD 4.19 dengan Fedora CoreOS di KVM <span class="version-badge">v3</span></h1>
        
        <div class="progress-indicator"></div>
        
        <div class="changelog">
            <h2>🚀 Changelog v3 - Complete Edition</h2>
            <ul>
                <li>✅ <strong>Complete tutorial</strong> dari awal sampai akhir dalam satu file</li>
                <li>✅ <strong>Dual configuration support</strong>: Standard cluster (3M+3W) dan Compact cluster (3M+1W)</li>
                <li>✅ <strong>Modern ignition-based approach</strong> dengan direct FCOS image injection</li>
                <li>✅ <strong>Latest versions</strong>: FCOS 42.20250705.3.0 dan OKD 4.19.0-okd-scos.10</li>
                <li>✅ <strong>Comprehensive automation</strong> dengan Ansible playbooks</li>
                <li>✅ <strong>Production-ready configuration</strong> dengan proper SELinux dan security</li>
                <li>✅ <strong>Troubleshooting guide</strong> dan best practices</li>
            </ul>
        </div>

        <div class="info">
            <strong>🎯 Target Audience:</strong> Tutorial v3 ini dirancang untuk system administrators, DevOps engineers, dan developers yang ingin menginstall OKD 4.19 dengan pendekatan modern dan production-ready.
        </div>

        <div class="toc">
            <h2>📋 Daftar Isi</h2>
            <ul>
                <li><a href="#overview">1. Overview dan Pilihan Konfigurasi</a></li>
                <li><a href="#persiapan">2. Persiapan Environment</a></li>
                <li><a href="#kvm-setup">3. Setup KVM Infrastructure</a></li>
                <li><a href="#bastion-vm">4. Buat dan Konfigurasi Bastion VM</a></li>
                <li><a href="#services">5. Setup Services di Bastion</a></li>
                <li><a href="#fcos-images">6. Download dan Prepare FCOS Images</a></li>
                <li><a href="#okd-installer">7. Download OKD Installer dan CLI</a></li>
                <li><a href="#ignition">8. Generate Ignition Files</a></li>
                <li><a href="#cluster-vms">9. Buat Cluster VMs dengan Ignition</a></li>
                <li><a href="#install">10. Install dan Monitor OKD Cluster</a></li>
                <li><a href="#post-install">11. Post-Installation Configuration</a></li>
                <li><a href="#troubleshooting">12. Troubleshooting dan Maintenance</a></li>
            </ul>
        </div>

        <h2 id="overview">1. Overview dan Pilihan Konfigurasi</h2>
        
        <h3>Pilihan Konfigurasi Cluster</h3>
        
        <div class="config-comparison">
            <div class="config-card standard-cluster">
                <h4>🏢 Standard Cluster</h4>
                <p><strong>Recommended untuk Production</strong></p>
                <ul>
                    <li>Bootstrap + 3 Masters + 3 Workers = 7 VMs</li>
                    <li>Total Resources: 20 vCPU, 76GB RAM, 370GB Storage</li>
                    <li>Masters dedicated untuk control plane</li>
                    <li>Workers dedicated untuk workloads</li>
                    <li>High availability dan scalability</li>
                </ul>
            </div>
            <div class="config-card compact-cluster">
                <h4>💡 Compact Cluster</h4>
                <p><strong>Recommended untuk Lab/Development</strong></p>
                <ul>
                    <li>Bootstrap + 3 Masters (juga Worker) + 1 Worker = 5 VMs</li>
                    <li>Total Resources: 18 vCPU, 68GB RAM, 290GB Storage</li>
                    <li>Masters dual-role (control plane + workload)</li>
                    <li>Resource efficient</li>
                    <li>Perfect untuk testing dan development</li>
                </ul>
            </div>
        </div>

        <h3>Network Configuration</h3>
        <ul>
            <li><strong>Network Range:</strong> *************/24</li>
            <li><strong>Gateway:</strong> *************</li>
            <li><strong>Bastion IP:</strong> ***************</li>
            <li><strong>Domain:</strong> febryan.web.id.example</li>
            <li><strong>Cluster Name:</strong> okd4</li>
        </ul>

        <h3>VM Specifications</h3>
        
        <h4>Standard Cluster</h4>
        <table>
            <thead>
                <tr>
                    <th>VM Type</th>
                    <th>vCPU</th>
                    <th>RAM</th>
                    <th>Storage</th>
                    <th>IP Address</th>
                    <th>MAC Address</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Bastion</td>
                    <td>2</td>
                    <td>4GB</td>
                    <td>20GB</td>
                    <td>***************</td>
                    <td>Auto</td>
                </tr>
                <tr>
                    <td>Bootstrap</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>*************0</td>
                    <td>52:54:00:a4:db:5f</td>
                </tr>
                <tr>
                    <td>Master01</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>**************</td>
                    <td>52:54:00:8b:a1:17</td>
                </tr>
                <tr>
                    <td>Master02</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>*************2</td>
                    <td>52:54:00:ea:8b:9d</td>
                </tr>
                <tr>
                    <td>Master03</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>*************3</td>
                    <td>52:54:00:f8:87:c7</td>
                </tr>
                <tr>
                    <td>Worker01</td>
                    <td>2</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>192.168.101.21</td>
                    <td>52:54:00:31:4a:39</td>
                </tr>
                <tr>
                    <td>Worker02</td>
                    <td>2</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>192.168.101.22</td>
                    <td>52:54:00:6a:37:32</td>
                </tr>
                <tr>
                    <td>Worker03</td>
                    <td>2</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>**************</td>
                    <td>52:54:00:95:d4:ed</td>
                </tr>
            </tbody>
        </table>

        <h4>Compact Cluster</h4>
        <table>
            <thead>
                <tr>
                    <th>VM Type</th>
                    <th>vCPU</th>
                    <th>RAM</th>
                    <th>Storage</th>
                    <th>IP Address</th>
                    <th>MAC Address</th>
                    <th>Role</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Bastion</td>
                    <td>2</td>
                    <td>4GB</td>
                    <td>20GB</td>
                    <td>***************</td>
                    <td>Auto</td>
                    <td>Helper</td>
                </tr>
                <tr>
                    <td>Bootstrap</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>*************0</td>
                    <td>52:54:00:a4:db:5f</td>
                    <td>Bootstrap</td>
                </tr>
                <tr style="background-color: #fff3e0;">
                    <td>Master01</td>
                    <td>4</td>
                    <td><strong>16GB</strong></td>
                    <td>50GB</td>
                    <td>**************</td>
                    <td>52:54:00:8b:a1:17</td>
                    <td><strong>Master+Worker</strong></td>
                </tr>
                <tr style="background-color: #fff3e0;">
                    <td>Master02</td>
                    <td>4</td>
                    <td><strong>16GB</strong></td>
                    <td>50GB</td>
                    <td>*************2</td>
                    <td>52:54:00:ea:8b:9d</td>
                    <td><strong>Master+Worker</strong></td>
                </tr>
                <tr style="background-color: #fff3e0;">
                    <td>Master03</td>
                    <td>4</td>
                    <td><strong>16GB</strong></td>
                    <td>50GB</td>
                    <td>*************3</td>
                    <td>52:54:00:f8:87:c7</td>
                    <td><strong>Master+Worker</strong></td>
                </tr>
                <tr>
                    <td>Worker01</td>
                    <td>2</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>192.168.101.21</td>
                    <td>52:54:00:31:4a:39</td>
                    <td>Worker</td>
                </tr>
            </tbody>
        </table>

        <div class="resource-summary">
            <strong>Resource Comparison:</strong><br>
            Standard Cluster: 20 vCPU, 76GB RAM, 370GB Storage<br>
            Compact Cluster: 18 vCPU, 68GB RAM, 290GB Storage<br>
            <em>Savings: 2 vCPU, 8GB RAM, 80GB Storage</em>
        </div>

        <h2 id="persiapan">2. Persiapan Environment</h2>

        <h3><span class="step-number">1</span>Verifikasi System Requirements</h3>

        <h4>Hardware Requirements</h4>
        <div class="command-block">
            <pre><code># Minimum requirements
# Standard Cluster: 32GB RAM, 8 CPU cores, 500GB storage
# Compact Cluster: 24GB RAM, 6 CPU cores, 350GB storage

# Check available resources
free -h
nproc
df -h /

# Check virtualization support
cat /proc/cpuinfo | egrep "vmx|svm"
ls -la /dev/kvm</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h4>Software Requirements</h4>
        <div class="command-block">
            <pre><code># Check OS version
cat /etc/os-release

# For RHEL/CentOS/Fedora
hostnamectl

# For Ubuntu/Debian
lsb_release -a</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">2</span>Install KVM dan Dependencies</h3>

        <h4>RHEL/CentOS/Fedora</h4>
        <div class="command-block">
            <pre><code># Install KVM packages
sudo dnf install -y qemu-kvm libvirt virt-install virt-manager virt-builder \
  bridge-utils python3-libvirt python3-lxml ansible git wget curl \
  bash-completion tree tar libselinux-python3

# Start dan enable libvirt
sudo systemctl enable --now libvirtd

# Add user to libvirt group
sudo usermod -aG libvirt $USER

# Logout dan login kembali untuk group changes</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h4>Ubuntu/Debian</h4>
        <div class="command-block">
            <pre><code># Update package list
sudo apt update

# Install KVM packages
sudo apt install -y qemu-kvm libvirt-daemon-system libvirt-clients \
  bridge-utils virt-manager virt-builder python3-libvirt ansible \
  git wget curl bash-completion tree tar

# Start dan enable libvirt
sudo systemctl enable --now libvirtd

# Add user to libvirt group
sudo usermod -aG libvirt $USER

# Logout dan login kembali untuk group changes</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="ignition">8. Generate Ignition Files</h2>

        <h3><span class="step-number">1</span>Pilih Konfigurasi Cluster</h3>

        <div class="warning">
            <strong>⚠️ Penting:</strong> Pilih salah satu konfigurasi di bawah ini sesuai dengan kebutuhan Anda. Jangan jalankan keduanya!
        </div>

        <h4>Untuk Standard Cluster (3 Masters + 3 Workers)</h4>
        <div class="command-block">
            <pre><code># Create directory
mkdir -p ~/okd4-standard
cd ~/

# Create install config
cat &lt;&lt;EOF &gt; install-config-standard.yaml
apiVersion: v1
baseDomain: febryan.web.id.example
compute:
- hyperthreading: Enabled
  name: worker
  replicas: 3  # 3 dedicated workers
controlPlane:
  hyperthreading: Enabled
  name: master
  replicas: 3
metadata:
  name: okd4
networking:
  clusterNetworks:
  - cidr: **********/14
    hostPrefix: 23
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
fips: false
sshKey: '\$(< ~/.ssh/id_rsa.pub)'
EOF

# Copy install config
cp install-config-standard.yaml okd4-standard/install-config.yaml
cd okd4-standard

# Generate manifests
openshift-install create manifests

# Disable scheduling pada master nodes (untuk standard cluster)
sed -i 's/true/false/' manifests/cluster-scheduler-02-config.yml

# Generate ignition files
openshift-install create ignition-configs

# Set SELinux context
chcon -t svirt_home_t *.ign

# Verify files
ls -laZ *.ign</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h4>Untuk Compact Cluster (3 Masters sebagai Workers + 1 Worker)</h4>
        <div class="command-block">
            <pre><code># Create directory
mkdir -p ~/okd4-compact
cd ~/

# Create install config
cat &lt;&lt;EOF &gt; install-config-compact.yaml
apiVersion: v1
baseDomain: febryan.web.id.example
compute:
- hyperthreading: Enabled
  name: worker
  replicas: 1  # 1 dedicated worker saja
controlPlane:
  hyperthreading: Enabled
  name: master
  replicas: 3
metadata:
  name: okd4
networking:
  clusterNetworks:
  - cidr: **********/14
    hostPrefix: 23
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
fips: false
sshKey: '\$(< ~/.ssh/id_rsa.pub)'
EOF

# Copy install config
cp install-config-compact.yaml okd4-compact/install-config.yaml
cd okd4-compact

# Generate manifests
openshift-install create manifests

# JANGAN disable scheduling pada master nodes (untuk compact cluster)
# Masters akan berfungsi sebagai workers juga

# Generate ignition files
openshift-install create ignition-configs

# Set SELinux context
chcon -t svirt_home_t *.ign

# Verify files
ls -laZ *.ign</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="cluster-vms">9. Buat Cluster VMs dengan Ignition</h2>

        <div class="info">
            <strong>🆕 Modern Approach:</strong> Tutorial v3 menggunakan direct ignition injection via qemu-commandline, menghilangkan ketergantungan pada PXE boot untuk cluster VMs.
        </div>

        <h3><span class="step-number">2</span>Buat Bootstrap VM</h3>
        <div class="command-block">
            <pre><code># Bootstrap VM dengan ignition
IGNITION_CONFIG="$IGNITION_DIR/bootstrap.ign"
VM_NAME="bootstrap.okd4.febryan.web.id.example"
VCPUS="4"
RAM_MB="8192"
DISK_GB="50"

# Set SELinux context
chcon -t svirt_home_t ${IGNITION_CONFIG}

# Create VM
virt-install --connect="qemu:///system" \
  --name="${VM_NAME}" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:a4:db:5f \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

echo "Bootstrap VM created: $VM_NAME"</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <div class="success">
            <h3>🎉 Selamat!</h3>
            <p>Jika Anda telah mengikuti semua langkah dengan benar, Anda sekarang memiliki:</p>
            <ul>
                <li>✅ <strong>Complete OKD 4.19 cluster</strong> yang fully functional</li>
                <li>✅ <strong>Modern ignition-based deployment</strong> dengan FCOS</li>
                <li>✅ <strong>Production-ready infrastructure</strong> dengan proper security</li>
                <li>✅ <strong>Flexible configuration</strong> (Standard atau Compact cluster)</li>
                <li>✅ <strong>Comprehensive monitoring</strong> dan troubleshooting tools</li>
            </ul>
            <p><strong>Your OKD cluster is ready for production workloads!</strong> 🚀</p>
        </div>

        <div class="info">
            <h3>📚 Next Steps</h3>
            <p>Setelah cluster berhasil diinstall, Anda dapat:</p>
            <ul>
                <li>Deploy aplikasi menggunakan <code>oc</code> CLI atau Web Console</li>
                <li>Setup CI/CD pipelines dengan Tekton atau Jenkins</li>
                <li>Configure monitoring dan alerting dengan Prometheus</li>
                <li>Implement GitOps dengan ArgoCD</li>
                <li>Scale cluster sesuai kebutuhan workload</li>
            </ul>
        </div>

        <div class="warning">
            <h3>⚠️ Important Notes</h3>
            <ul>
                <li><strong>Backup regularly:</strong> Backup ignition files, cluster configuration, dan persistent data</li>
                <li><strong>Monitor resources:</strong> Keep track of CPU, memory, dan storage usage</li>
                <li><strong>Update strategy:</strong> Plan cluster updates during maintenance windows</li>
                <li><strong>Security:</strong> Regularly update dan patch underlying infrastructure</li>
            </ul>
        </div>

    </div>

    <button class="nav-top" onclick="scrollToTop()">↑ Top</button>

    <script>
        function copyToClipboard(button) {
            const codeBlock = button.previousElementSibling;
            const text = codeBlock.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                button.textContent = 'Copied!';
                button.style.background = 'linear-gradient(45deg, #28a745, #20c997)';
                setTimeout(function() {
                    button.textContent = 'Copy';
                    button.style.background = 'linear-gradient(45deg, #6c757d, #5a6268)';
                }, 2000);
            });
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Progress indicator animation
        window.addEventListener('scroll', function() {
            const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            document.querySelector('.progress-indicator').style.width = scrolled + '%';
        });
    </script>
</body>
</html>
