# Tutorial: OKD 4.19 Compact Cluster (3 Master + 1 Worker) dengan Fedora CoreOS

## Daftar Isi
1. [Overview Compact Cluster](#overview-compact-cluster)
2. [Persiapan Environment](#persiapan-environment)
3. [Setup Infrastructure](#setup-infrastructure)
4. [Generate Ignition Files](#generate-ignition-files)
5. [Buat Cluster VMs](#buat-cluster-vms)
6. [Install Compact Cluster](#install-compact-cluster)
7. [Post-Installation](#post-installation)

## Overview Compact Cluster

### Apa itu Compact Cluster?
Compact cluster adalah konfigurasi OKD dimana master nodes juga berfungsi sebagai worker nodes. Ini mengurangi jumlah VM yang diperlukan dan cocok untuk:
- **Development environment**
- **Testing dan lab**
- **Resource-constrained environment**
- **Small production workloads**

### Arsitektur Compact Cluster
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Bootstrap     │    │   Master01      │    │   Master02      │    │   Master03      │
│                 │    │ (Master+Worker) │    │ (Master+Worker) │    │ (Master+Worker) │
│ **************  │    │ **************  │    │ **************  │    │ **************  │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
                                                                      
                       ┌─────────────────┐
                       │   Worker01      │
                       │ (Dedicated)     │
                       │ **************  │
                       └─────────────────┘
```

### Spesifikasi VM Compact Cluster
| VM Type | vCPU | RAM | Storage | IP Address | Role |
|---------|------|-----|---------|------------|------|
| Bastion | 2 | 4GB | 20GB | *************** | Helper |
| Bootstrap | 4 | 8GB | 50GB | ************** | Bootstrap |
| Master01 | 4 | 16GB | 50GB | ************** | Master+Worker |
| Master02 | 4 | 16GB | 50GB | ************** | Master+Worker |
| Master03 | 4 | 16GB | 50GB | ************** | Master+Worker |
| Worker01 | 2 | 8GB | 50GB | ************** | Worker |

**Total Resources:** 18 vCPU, 68GB RAM, 290GB Storage

## Persiapan Environment

### 1. Setup Infrastructure (Sama seperti tutorial utama)
```bash
# Ikuti langkah 1-8 dari tutorial utama untuk:
# - Setup KVM infrastructure
# - Buat bastion VM
# - Setup DHCP, DNS, HAProxy
# - Download OKD installer dan FCOS images
```

### 2. Update Variables untuk Compact Cluster
```bash
# Edit vars/main.yml untuk compact cluster
cat <<EOF > vars/main.yml
---
ppc64le: false
uefi: false
disk: vda
helper:
  name: "bastion"
  ipaddr: "***************"
  networkifacename: "enp1s0"
dns:
  domain: "febryan.web.id.example"
  clusterid: "okd4"
  forwarder1: "*******"
  forwarder2: "*******"
  lb_ipaddr: "{{ helper.ipaddr }}"
dhcp:
  router: "*************"
  bcast: "***************"
  netmask: "*************"
  poolstart: "**************"
  poolend: "**************"
  ipid: "*************"
  netmaskid: "*************"
  ntp: "time.google.com"
  dns: ""
bootstrap:
  name: "bootstrap"
  ipaddr: "**************"
  macaddr: "52:54:00:a4:db:5f"
masters:
  - name: "master01"
    ipaddr: "**************"
    macaddr: "52:54:00:8b:a1:17"
  - name: "master02"
    ipaddr: "**************"
    macaddr: "52:54:00:ea:8b:9d"
  - name: "master03"
    ipaddr: "**************"
    macaddr: "52:54:00:f8:87:c7"
workers:
  - name: "worker01"
    ipaddr: "**************"
    macaddr: "52:54:00:31:4a:39"
EOF
```

## Generate Ignition Files

### 1. Buat Install Config untuk Compact Cluster
```bash
# Generate SSH key
ssh-keygen -t rsa -N "" -f ~/.ssh/id_rsa

# Buat direktori untuk OKD
mkdir -p ~/okd4-compact
cd ~/

# Install config untuk compact cluster (1 worker saja)
cat <<EOF > install-config-compact.yaml
apiVersion: v1
baseDomain: febryan.web.id.example
compute:
- hyperthreading: Enabled
  name: worker
  replicas: 1  # Hanya 1 dedicated worker
controlPlane:
  hyperthreading: Enabled
  name: master
  replicas: 3
metadata:
  name: okd4
networking:
  clusterNetworks:
  - cidr: **********/14
    hostPrefix: 23
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
fips: false
sshKey: '$(< ~/.ssh/id_rsa.pub)'
EOF
```

### 2. Generate Ignition Files
```bash
# Copy install config
cp install-config-compact.yaml okd4-compact/install-config.yaml
cd okd4-compact

# Generate manifests
openshift-install create manifests

# PENTING: Enable scheduling pada master nodes untuk compact cluster
# Jangan disable scheduling seperti pada cluster biasa
# sed -i 's/true/false/' manifests/cluster-scheduler-02-config.yml  # SKIP ini untuk compact

# Generate ignition files
openshift-install create ignition-configs

# Set SELinux context untuk ignition files
sudo chcon -t svirt_home_t *.ign

# Verifikasi files
ls -la *.ign
```

## Buat Cluster VMs

### 1. Setup Variables
```bash
# Set variables
FCOS_IMAGE="$HOME/.local/share/libvirt/images/fedora-coreos-stable.qcow2"
IGNITION_DIR="$HOME/okd4-compact"
NETWORK_NAME="openshift4"

# Verifikasi files exist
ls -la $FCOS_IMAGE
ls -la $IGNITION_DIR/*.ign
```

### 2. Buat Bootstrap VM
```bash
# Bootstrap VM
IGNITION_CONFIG="$IGNITION_DIR/bootstrap.ign"
VM_NAME="bootstrap.okd4.febryan.web.id.example"
VCPUS="4"
RAM_MB="8192"
DISK_GB="50"

sudo chcon -t svirt_home_t ${IGNITION_CONFIG}

virt-install --connect="qemu:///system" \
  --name="${VM_NAME}" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:a4:db:5f \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole
```

### 3. Buat Master VMs (dengan RAM lebih besar untuk dual role)
```bash
# Master VMs dengan ignition (RAM 16GB untuk dual role)
IGNITION_CONFIG="$IGNITION_DIR/master.ign"
VCPUS="4"
RAM_MB="16384"  # 16GB untuk master+worker role
DISK_GB="50"

sudo chcon -t svirt_home_t ${IGNITION_CONFIG}

# Master01
virt-install --connect="qemu:///system" \
  --name="master01.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:8b:a1:17 \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

# Master02
virt-install --connect="qemu:///system" \
  --name="master02.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:ea:8b:9d \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

# Master03
virt-install --connect="qemu:///system" \
  --name="master03.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:f8:87:c7 \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole
```

### 4. Buat Worker VM (1 saja)
```bash
# Worker VM dengan ignition (hanya 1)
IGNITION_CONFIG="$IGNITION_DIR/worker.ign"
VCPUS="2"
RAM_MB="8192"
DISK_GB="50"

sudo chcon -t svirt_home_t ${IGNITION_CONFIG}

# Worker01 (dedicated worker)
virt-install --connect="qemu:///system" \
  --name="worker01.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:31:4a:39 \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole
```

## Install Compact Cluster

### 1. Start Bootstrap Process
```bash
# Start bootstrap VM
sudo virsh start bootstrap.okd4.febryan.web.id.example

# Monitor bootstrap process
cd ~/okd4-compact
openshift-install --dir=. wait-for bootstrap-complete --log-level=info
```

### 2. Start Master VMs
```bash
# Start master VMs setelah bootstrap selesai
sudo virsh start master01.okd4.febryan.web.id.example
sudo virsh start master02.okd4.febryan.web.id.example
sudo virsh start master03.okd4.febryan.web.id.example

# Export kubeconfig
export KUBECONFIG=~/okd4-compact/auth/kubeconfig

# Wait for masters to be ready
oc get nodes
```

### 3. Approve CSRs dan Start Worker
```bash
# Approve CSRs untuk master nodes
oc get csr -o go-template='{{range .items}}{{if not .status}}{{.metadata.name}}{{"\n"}}{{end}}{{end}}' | xargs oc adm certificate approve

# Start dedicated worker VM
sudo virsh start worker01.okd4.febryan.web.id.example

# Approve CSRs untuk worker node
oc get csr -o go-template='{{range .items}}{{if not .status}}{{.metadata.name}}{{"\n"}}{{end}}{{end}}' | xargs oc adm certificate approve

# Wait for installation completion
openshift-install --dir=. wait-for install-complete --log-level=info
```

### 4. Verifikasi Compact Cluster
```bash
# Check nodes (should show masters as schedulable)
oc get nodes

# Check node roles
oc get nodes --show-labels | grep node-role

# Verify masters can schedule workloads
oc describe node master01.okd4.febryan.web.id.example | grep Taints

# Check cluster operators
oc get co
```

## Post-Installation

### 1. Configure Image Registry
```bash
# Configure image registry
oc patch configs.imageregistry.operator.openshift.io cluster --type merge --patch '{"spec":{"storage":{"emptyDir":{}}}}'
oc patch configs.imageregistry.operator.openshift.io cluster --type merge --patch '{"spec":{"managementState":"Managed"}}'
```

### 2. Test Workload Scheduling
```bash
# Deploy test workload
cat <<EOF | oc apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-compact
  namespace: default
spec:
  replicas: 6
  selector:
    matchLabels:
      app: test-compact
  template:
    metadata:
      labels:
        app: test-compact
    spec:
      containers:
      - name: nginx
        image: nginx:latest
        ports:
        - containerPort: 80
EOF

# Check pod distribution
oc get pods -o wide

# Should see pods scheduled on both masters and worker
```

### 3. Monitor Resource Usage
```bash
# Check node resource usage
oc adm top nodes

# Check pod resource usage
oc adm top pods --all-namespaces

# Monitor cluster capacity
oc describe nodes | grep -A5 "Allocated resources"
```

## Keuntungan dan Keterbatasan

### ✅ Keuntungan Compact Cluster
- **Resource Efficiency**: Mengurangi jumlah VM dari 7 menjadi 5
- **Cost Effective**: Hemat resource untuk lab/development
- **Simplified Management**: Fewer nodes to manage
- **Quick Setup**: Faster deployment

### ⚠️ Keterbatasan Compact Cluster
- **Resource Contention**: Master dan worker workloads compete for resources
- **Limited Scalability**: Tidak cocok untuk production scale
- **Performance Impact**: Control plane dan workload sharing resources
- **Upgrade Complexity**: Master updates affect workload availability

## Troubleshooting

### Common Issues
1. **Masters tidak schedulable**
   ```bash
   # Check taints
   oc describe nodes | grep Taints
   
   # Remove NoSchedule taint if present
   oc adm taint nodes master01.okd4.febryan.web.id.example node-role.kubernetes.io/master:NoSchedule-
   ```

2. **Resource pressure**
   ```bash
   # Check resource usage
   oc adm top nodes
   
   # Check events for resource issues
   oc get events --sort-by='.lastTimestamp'
   ```

3. **Pod scheduling issues**
   ```bash
   # Check pod status
   oc get pods --all-namespaces
   
   # Describe problematic pods
   oc describe pod <pod-name>
   ```

## Kesimpulan

Compact cluster adalah solusi yang baik untuk:
- **Development dan testing**
- **Resource-constrained environments**
- **Learning dan lab setup**
- **Small production workloads**

Dengan konfigurasi 3 master (yang juga worker) + 1 dedicated worker, Anda mendapatkan cluster OKD yang functional dengan resource requirement yang lebih rendah.

## Referensi

- [OKD Documentation](https://docs.okd.io/)
- [OpenShift Compact Clusters](https://docs.openshift.com/container-platform/4.19/installing/installing_bare_metal/installing-bare-metal.html#installation-three-node-cluster_installing-bare-metal)
- [Fedora CoreOS Documentation](https://docs.fedoraproject.org/en-US/fedora-coreos/)
