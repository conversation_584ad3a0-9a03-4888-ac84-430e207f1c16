<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tutorial Lengkap: Install OKD 4.19 dengan Fedora CoreOS di KVM</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #d73027;
            border-bottom: 3px solid #d73027;
            padding-bottom: 10px;
            text-align: center;
        }
        h2 {
            color: #2166ac;
            border-left: 4px solid #2166ac;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #5aae61;
            margin-top: 25px;
        }
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .toc h2 {
            margin-top: 0;
            color: #495057;
            border: none;
            padding: 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #007bff;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 3px;
            transition: background-color 0.3s;
        }
        .toc a:hover {
            background-color: #e9ecef;
        }
        pre {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
            border-left: 4px solid #4299e1;
        }
        code {
            background-color: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            color: #d73027;
        }
        pre code {
            background-color: transparent;
            padding: 0;
            color: #e2e8f0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-left: 4px solid #f39c12;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 4px solid #17a2b8;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .step-number {
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .command-block {
            position: relative;
        }
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .copy-btn:hover {
            background-color: #5a6268;
        }
        .nav-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 50px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            z-index: 1000;
        }
        .nav-top:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tutorial Lengkap: Install OKD 4.19 dengan Fedora CoreOS di KVM</h1>
        
        <div class="info">
            <strong>Info:</strong> Tutorial ini memberikan panduan lengkap untuk menginstall OKD 4.19 menggunakan Fedora CoreOS di environment KVM. Tutorial ini disesuaikan dengan konfigurasi network 192.168.101.x dan menggunakan domain febryan.web.id.example.
        </div>

        <div class="toc">
            <h2>Daftar Isi</h2>
            <ul>
                <li><a href="#persiapan">1. Persiapan Environment</a></li>
                <li><a href="#kvm-setup">2. Setup KVM Infrastructure</a></li>
                <li><a href="#bastion-vm">3. Buat Bastion VM</a></li>
                <li><a href="#ansible">4. Install Ansible dan Konfigurasi</a></li>
                <li><a href="#dhcp">5. Setup DHCP Server</a></li>
                <li><a href="#dns">6. Setup DNS Server</a></li>
                <li><a href="#tftp">7. Setup TFTP/PXE Server</a></li>
                <li><a href="#haproxy">8. Setup HAProxy Load Balancer</a></li>
                <li><a href="#okd-installer">9. Download OKD Installer dan CLI</a></li>
                <li><a href="#ignition">10. Generate Ignition Files</a></li>
                <li><a href="#cluster-vms">11. Buat Cluster VMs</a></li>
                <li><a href="#install">12. Install OKD Cluster</a></li>
                <li><a href="#troubleshooting">13. Troubleshooting</a></li>
            </ul>
        </div>

        <h2 id="specs">Spesifikasi Environment</h2>
        
        <h3>Network Configuration</h3>
        <ul>
            <li><strong>Network Range:</strong> *************/24</li>
            <li><strong>Gateway:</strong> *************</li>
            <li><strong>Bastion IP:</strong> ***************</li>
            <li><strong>Domain:</strong> febryan.web.id.example</li>
            <li><strong>Cluster Name:</strong> okd4</li>
        </ul>

        <h3>VM Requirements</h3>
        <table>
            <thead>
                <tr>
                    <th>VM Type</th>
                    <th>vCPU</th>
                    <th>RAM</th>
                    <th>Storage</th>
                    <th>IP Address</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Bastion</td>
                    <td>2</td>
                    <td>4GB</td>
                    <td>20GB</td>
                    <td>***************</td>
                </tr>
                <tr>
                    <td>Bootstrap</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>*************0</td>
                </tr>
                <tr>
                    <td>Master01</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>*************1</td>
                </tr>
                <tr>
                    <td>Master02</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>**************</td>
                </tr>
                <tr>
                    <td>Master03</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>*************3</td>
                </tr>
                <tr>
                    <td>Worker01</td>
                    <td>2</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>192.168.101.21</td>
                </tr>
                <tr>
                    <td>Worker02</td>
                    <td>2</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>**************</td>
                </tr>
                <tr>
                    <td>Worker03</td>
                    <td>2</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>**************</td>
                </tr>
            </tbody>
        </table>

        <h3>MAC Addresses</h3>
        <table>
            <thead>
                <tr>
                    <th>VM</th>
                    <th>MAC Address</th>
                </tr>
            </thead>
            <tbody>
                <tr><td>Bootstrap</td><td>52:54:00:a4:db:5f</td></tr>
                <tr><td>Master01</td><td>52:54:00:8b:a1:17</td></tr>
                <tr><td>Master02</td><td>52:54:00:ea:8b:9d</td></tr>
                <tr><td>Master03</td><td>52:54:00:f8:87:c7</td></tr>
                <tr><td>Worker01</td><td>52:54:00:31:4a:39</td></tr>
                <tr><td>Worker02</td><td>52:54:00:6a:37:32</td></tr>
                <tr><td>Worker03</td><td>52:54:00:95:d4:ed</td></tr>
            </tbody>
        </table>

        <h2 id="persiapan">1. Persiapan Environment</h2>
        
        <h3><span class="step-number">1</span>Verifikasi CPU Virtualization Support</h3>
        <div class="command-block">
            <pre><code>cat /proc/cpuinfo | egrep "vmx|svm"</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">2</span>Install KVM Packages</h3>
        <div class="command-block">
            <pre><code># Ubuntu/Debian
sudo apt update
sudo apt install -y qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils virt-manager virt-builder

# RHEL/CentOS/Fedora
sudo dnf install -y qemu-kvm libvirt virt-install virt-manager virt-builder

# Start dan enable libvirt
sudo systemctl enable --now libvirtd
sudo usermod -aG libvirt $USER</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="kvm-setup">2. Setup KVM Infrastructure</h2>
        
        <h3><span class="step-number">1</span>Buat Virtual Network</h3>
        <div class="command-block">
            <pre><code># Buat file konfigurasi network
cat &lt;&lt;EOF &gt; openshift4.xml
&lt;network&gt;
  &lt;name&gt;openshift4&lt;/name&gt;
  &lt;forward mode='nat'&gt;
    &lt;nat&gt;
      &lt;port start='1024' end='65535'/&gt;
    &lt;/nat&gt;
  &lt;/forward&gt;
  &lt;bridge name='openshift4' stp='on' delay='0'/&gt;
  &lt;domain name='openshift4'/&gt;
  &lt;ip address='*************' netmask='*************'&gt;
  &lt;/ip&gt;
&lt;/network&gt;
EOF

# Define dan start network
sudo virsh net-define --file openshift4.xml
sudo virsh net-autostart openshift4
sudo virsh net-start openshift4

# Verifikasi
sudo virsh net-list
brctl show</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="bastion-vm">3. Buat Bastion VM</h2>
        
        <h3><span class="step-number">1</span>Download dan Buat VM Image</h3>
        <div class="command-block">
            <pre><code># Download Fedora 42 template
sudo virt-builder fedora-42 --format qcow2 \
  --size 20G -o /var/lib/libvirt/images/okd-bastion-server.qcow2 \
  --root-password password:rahasia

# Buat VM
sudo virt-install \
  --name okd-bastion-server \
  --ram 4096 \
  --vcpus 2 \
  --disk path=/var/lib/libvirt/images/okd-bastion-server.qcow2 \
  --os-variant rhel9.0 \
  --network bridge=openshift4,model=virtio \
  --graphics none \
  --serial pty \
  --console pty \
  --boot hd \
  --import</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">2</span>Konfigurasi Network di Bastion VM</h3>
        <div class="command-block">
            <pre><code># Login ke VM (password: rahasia)
sudo virsh console okd-bastion-server

# Di dalam VM, setup network
nmcli con add type ethernet con-name enp1s0 ifname enp1s0 \
  connection.autoconnect yes ipv4.method manual \
  ipv4.address ***************/24 ipv4.gateway ************* \
  ipv4.dns *******

# Test koneksi
ping -c 2 *******

# Update sistem
sudo dnf -y upgrade
sudo dnf -y install git vim wget curl bash-completion tree tar libselinux-python3 firewalld

# Reboot
sudo reboot

# Enable autostart
sudo virsh autostart okd-bastion-server</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="ansible">4. Install Ansible dan Konfigurasi</h2>

        <h3><span class="step-number">1</span>Install Ansible di Bastion VM</h3>
        <div class="command-block">
            <pre><code># Di Bastion VM
sudo dnf -y install git ansible vim wget curl bash-completion tree tar libselinux-python3

# Clone repository
cd ~/
git clone https://github.com/jmutai/ocp4_ansible.git
cd ~/ocp4_ansible</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">2</span>Konfigurasi Ansible</h3>
        <div class="command-block">
            <pre><code># Edit ansible.cfg
cat &lt;&lt;EOF &gt; ansible.cfg
[defaults]
inventory = inventory
command_warnings = False
host_key_checking = False
deprecation_warnings = False
retry_files = false

[privilege_escalation]
become = true
become_method = sudo
become_user = root
become_ask_pass = false
EOF

# Setup inventory
cat &lt;&lt;EOF &gt; inventory
[vms_host]
localhost ansible_connection=local
EOF</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="dhcp">5. Setup DHCP Server</h2>

        <h3><span class="step-number">1</span>Install dan Konfigurasi DHCP</h3>
        <div class="command-block">
            <pre><code># Install DHCP server
sudo dnf -y install dhcp-server
sudo systemctl enable dhcpd

# Backup konfigurasi default
sudo mv /etc/dhcp/dhcpd.conf /etc/dhcp/dhcpd.conf.bak

# Jalankan konfigurasi dengan Ansible
ansible-playbook tasks/configure_dhcpd.yml

# Verifikasi
systemctl status dhcpd
cat /etc/dhcp/dhcpd.conf</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="dns">6. Setup DNS Server</h2>

        <h3><span class="step-number">1</span>Install dan Konfigurasi DNS</h3>
        <div class="command-block">
            <pre><code># Install BIND
sudo dnf -y install bind bind-utils
sudo systemctl enable named

# Install DNS serial script
sudo tee /usr/local/bin/set-dns-serial.sh&lt;&lt;EOF
#!/bin/bash
dnsserialfile=/usr/local/src/dnsserial-DO_NOT_DELETE_BEFORE_ASKING_CHRISTIAN.txt
zonefile=/var/named/zonefile.db
if [ -f zonefile ] ; then
    echo \$[ \$(grep serial \${zonefile} | tr -d "\t"" ""\n" | cut -d';' -f 1) + 1 ] | tee \${dnsserialfile}
else
    if [ ! -f \${dnsserialfile} ] || [ ! -s \${dnsserialfile} ]; then
        echo \$(date +%Y%m%d00) | tee \${dnsserialfile}
    else
        echo \$[ \$(< \${dnsserialfile}) + 1 ] | tee \${dnsserialfile}
    fi
fi
EOF

sudo chmod a+x /usr/local/bin/set-dns-serial.sh

# Konfigurasi DNS dengan Ansible
ansible-playbook tasks/configure_bind_dns.yml</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="tftp">7. Setup TFTP/PXE Server</h2>

        <h3><span class="step-number">1</span>Install TFTP Server</h3>
        <div class="command-block">
            <pre><code># Install packages
sudo dnf -y install tftp-server syslinux
sudo firewall-cmd --add-service=tftp --permanent
sudo firewall-cmd --reload

# Setup TFTP directories
sudo mkdir -p /var/lib/tftpboot/pxelinux.cfg
sudo cp -rvf /usr/share/syslinux/* /var/lib/tftpboot
sudo mkdir -p /var/lib/tftpboot/fcos</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">2</span>Download Fedora CoreOS Images</h3>
        <div class="warning">
            <strong>Perhatian:</strong> Pastikan menggunakan FCOS images terbaru untuk kompatibilitas dengan OKD 4.19.
        </div>
        <div class="command-block">
            <pre><code># Download FCOS 42 images (terbaru)
mkdir -p ~/fcos-images
cd ~/fcos-images

wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-live-kernel.x86_64
wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-live-initramfs.x86_64.img
wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-live-rootfs.x86_64.img

# Move ke lokasi yang benar
sudo mv fedora-coreos-42.20250705.3.0-live-kernel.x86_64 /var/lib/tftpboot/fcos/kernel
sudo mv fedora-coreos-42.20250705.3.0-live-initramfs.x86_64.img /var/lib/tftpboot/fcos/initramfs.img

# Setup web server untuk rootfs
sudo dnf -y install httpd
sudo mkdir -p /var/www/html/fcos
sudo mv fedora-coreos-42.20250705.3.0-live-rootfs.x86_64.img /var/www/html/fcos/rootfs.img

# Set permissions
sudo chmod 644 /var/lib/tftpboot/fcos/*
sudo chmod 644 /var/www/html/fcos/*
sudo restorecon -RFv /var/lib/tftpboot/fcos
sudo restorecon -RFv /var/www/html/fcos</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="haproxy">8. Setup HAProxy Load Balancer</h2>

        <h3><span class="step-number">1</span>Install dan Konfigurasi HAProxy</h3>
        <div class="command-block">
            <pre><code># Install HAProxy
sudo dnf install -y haproxy
sudo setsebool -P haproxy_connect_any 1

# Konfigurasi dengan Ansible
ansible-playbook tasks/configure_haproxy_lb.yml

# Setup SELinux ports
sudo dnf install -y policycoreutils-python-utils
sudo semanage port -a 6443 -t http_port_t -p tcp
sudo semanage port -a 22623 -t http_port_t -p tcp

# Open firewall
sudo firewall-cmd --add-service={http,https} --permanent
sudo firewall-cmd --add-port={6443,22623}/tcp --permanent
sudo firewall-cmd --reload

# Start HAProxy
sudo systemctl enable --now haproxy</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="okd-installer">9. Download OKD Installer dan CLI</h2>

        <h3><span class="step-number">1</span>Download OKD 4.19 Binaries</h3>
        <div class="info">
            <strong>Info:</strong> Menggunakan versi OKD 4.19.0-okd-scos.10 yang merupakan versi terbaru dan stabil.
        </div>
        <div class="command-block">
            <pre><code>cd ~/

# Download OKD Client (terbaru)
wget https://github.com/okd-project/okd/releases/download/4.19.0-okd-scos.10/openshift-client-linux-4.19.0-okd-scos.10.tar.gz
tar xvf openshift-client-linux-4.19.0-okd-scos.10.tar.gz
sudo mv oc kubectl /usr/local/bin

# Download OKD Installer (terbaru)
wget https://github.com/okd-project/okd/releases/download/4.19.0-okd-scos.10/openshift-install-linux-4.19.0-okd-scos.10.tar.gz
tar xvf openshift-install-linux-4.19.0-okd-scos.10.tar.gz
sudo mv openshift-install /usr/local/bin

# Cleanup
rm -f *.tar.gz README.md LICENSE

# Verifikasi
openshift-install version
oc version --client</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="ignition">10. Generate Ignition Files</h2>

        <h3><span class="step-number">1</span>Buat SSH Key</h3>
        <div class="command-block">
            <pre><code># Generate SSH key untuk akses ke cluster nodes
ssh-keygen -t rsa -N "" -f ~/.ssh/id_rsa</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">2</span>Buat Install Config (Tanpa Pull Secret)</h3>
        <div class="success">
            <strong>Recommended:</strong> Untuk OKD, tidak diperlukan pull secret dari Red Hat. Gunakan konfigurasi ini untuk instalasi yang lebih sederhana.
        </div>
        <div class="command-block">
            <pre><code># Buat direktori untuk OKD
mkdir -p ~/okd4
cd ~/

# Install config tanpa pull secret (untuk OKD)
cat &lt;&lt;EOF &gt; install-config-okd-no-pull-secret.yaml
apiVersion: v1
baseDomain: febryan.web.id.example
compute:
- hyperthreading: Enabled
  name: worker
  replicas: 0
controlPlane:
  hyperthreading: Enabled
  name: master
  replicas: 3
metadata:
  name: okd4
networking:
  clusterNetworks:
  - cidr: **********/14
    hostPrefix: 23
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
fips: false
sshKey: '\$(< ~/.ssh/id_rsa.pub)'
EOF</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">3</span>Buat Install Config (Dengan Fake Pull Secret)</h3>
        <div class="warning">
            <strong>Optional:</strong> Gunakan ini hanya jika installer memerlukan pull secret. Biasanya tidak diperlukan untuk OKD.
        </div>
        <div class="command-block">
            <pre><code># Install config dengan fake pull secret (jika diperlukan)
cat &lt;&lt;EOF &gt; install-config-okd-fake-pull-secret.yaml
apiVersion: v1
baseDomain: febryan.web.id.example
compute:
- hyperthreading: Enabled
  name: worker
  replicas: 0
controlPlane:
  hyperthreading: Enabled
  name: master
  replicas: 3
metadata:
  name: okd4
networking:
  clusterNetworks:
  - cidr: **********/14
    hostPrefix: 23
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
fips: false
pullSecret: '{"auths":{"fake":{"auth":"************"}}}'
sshKey: '\$(< ~/.ssh/id_rsa.pub)'
EOF</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">4</span>Generate Ignition Files</h3>
        <div class="command-block">
            <pre><code># Gunakan config tanpa pull secret (recommended untuk OKD)
cp install-config-okd-no-pull-secret.yaml okd4/install-config.yaml
cd okd4

# Generate manifests
openshift-install create manifests

# Disable scheduling pada master nodes
sed -i 's/true/false/' manifests/cluster-scheduler-02-config.yml

# Generate ignition files
openshift-install create ignition-configs

# Copy ignition files ke web server
sudo mkdir -p /var/www/html/ignition
sudo cp -v *.ign /var/www/html/ignition/
sudo chmod 644 /var/www/html/ignition/*.ign
sudo restorecon -RFv /var/www/html/

# Verifikasi files
ls -la /var/www/html/ignition/
curl -I http://***************:8080/ignition/bootstrap.ign</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="cluster-vms">11. Buat Cluster VMs</h2>

        <h3><span class="step-number">1</span>Restart Semua Services</h3>
        <div class="command-block">
            <pre><code># Pastikan semua services berjalan
sudo systemctl enable --now haproxy dhcpd httpd tftp named
sudo systemctl restart haproxy dhcpd httpd tftp named
sudo systemctl status haproxy dhcpd httpd tftp named</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">2</span>Buat Bootstrap VM</h3>
        <div class="command-block">
            <pre><code># Bootstrap VM
sudo virt-install -n bootstrap.okd4.febryan.web.id.example \
  --description "Bootstrap Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=4 \
  --os-variant=rhel9.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:a4:db:5f

# Start bootstrap VM
sudo virsh start bootstrap.okd4.febryan.web.id.example</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">3</span>Buat Master VMs</h3>
        <div class="command-block">
            <pre><code># Master01 VM
sudo virt-install -n master01.okd4.febryan.web.id.example \
  --description "Master01 Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=4 \
  --os-variant=rhel9.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:8b:a1:17

# Master02 VM
sudo virt-install -n master02.okd4.febryan.web.id.example \
  --description "Master02 Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=4 \
  --os-variant=rhel9.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:ea:8b:9d

# Master03 VM
sudo virt-install -n master03.okd4.febryan.web.id.example \
  --description "Master03 Machine for OKD 4.19 Cluster" \
  --ram=8192 \
  --vcpus=4 \
  --os-variant=rhel9.0 \
  --noreboot \
  --disk pool=default,bus=virtio,size=50 \
  --graphics none \
  --serial pty \
  --console pty \
  --pxe \
  --network bridge=openshift4,mac=52:54:00:f8:87:c7</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="install">12. Install OKD Cluster</h2>

        <h3><span class="step-number">1</span>Monitor Bootstrap Process</h3>
        <div class="info">
            <strong>Info:</strong> Proses bootstrap biasanya memakan waktu 15-30 menit. Monitor log untuk memastikan tidak ada error.
        </div>
        <div class="command-block">
            <pre><code># Monitor bootstrap completion
cd ~/okd4
openshift-install --dir=. wait-for bootstrap-complete --log-level=info

# Jika bootstrap berhasil, start master VMs
sudo virsh start master01.okd4.febryan.web.id.example
sudo virsh start master02.okd4.febryan.web.id.example
sudo virsh start master03.okd4.febryan.web.id.example

# Monitor logs (di terminal terpisah)
journalctl -f -u tftp
journalctl -f -u dhcpd</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">2</span>Approve Certificate Signing Requests (CSRs)</h3>
        <div class="command-block">
            <pre><code># Export kubeconfig
export KUBECONFIG=~/okd4/auth/kubeconfig

# Check pending CSRs
oc get csr

# Approve pending CSRs untuk master nodes
oc get csr -o go-template='{{range .items}}{{if not .status}}{{.metadata.name}}{{"\n"}}{{end}}{{end}}' | xargs oc adm certificate approve</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">3</span>Complete Installation</h3>
        <div class="command-block">
            <pre><code># Wait for installation completion
openshift-install --dir=. wait-for install-complete --log-level=info

# Check cluster status
oc get nodes
oc get co  # cluster operators
oc get pods --all-namespaces

# Get cluster info
oc cluster-info
oc whoami --show-console</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="troubleshooting">13. Troubleshooting</h2>

        <h3>Common Issues dan Solutions</h3>

        <h4>1. Bootstrap VM tidak boot dari PXE</h4>
        <div class="command-block">
            <pre><code># Check DHCP logs
journalctl -f -u dhcpd

# Check TFTP logs
journalctl -f -u tftp

# Verify PXE files
ls -la /var/lib/tftpboot/pxelinux.cfg/</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h4>2. DNS Resolution Issues</h4>
        <div class="command-block">
            <pre><code># Test DNS
dig @*************** bootstrap.okd4.febryan.web.id.example
nslookup api.okd4.febryan.web.id.example ***************</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h4>3. Ignition Files tidak accessible</h4>
        <div class="command-block">
            <pre><code># Check httpd status
systemctl status httpd

# Test access
curl -I http://***************:8080/ignition/bootstrap.ign
curl -I http://***************:8080/fcos/rootfs.img</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <div class="success">
            <h3>Selamat!</h3>
            <p>Jika Anda telah mengikuti semua langkah dengan benar, Anda sekarang memiliki cluster OKD 4.19 yang fully functional dengan:</p>
            <ul>
                <li>3 Master nodes (Control Plane)</li>
                <li>3 Worker nodes (Compute)</li>
                <li>Load balancer (HAProxy)</li>
                <li>DNS server (BIND)</li>
                <li>DHCP server</li>
                <li>PXE/TFTP server</li>
            </ul>
            <p>Cluster Anda siap untuk deploy aplikasi!</p>
        </div>

    </div>

    <button class="nav-top" onclick="scrollToTop()">↑ Top</button>

    <script>
        function copyToClipboard(button) {
            const codeBlock = button.previousElementSibling;
            const text = codeBlock.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                button.textContent = 'Copied!';
                button.style.backgroundColor = '#28a745';
                setTimeout(function() {
                    button.textContent = 'Copy';
                    button.style.backgroundColor = '#6c757d';
                }, 2000);
            });
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
