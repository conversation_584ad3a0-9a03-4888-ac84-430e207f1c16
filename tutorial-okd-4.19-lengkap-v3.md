# Tutorial Lengkap: Install OKD 4.19 dengan Fedora CoreOS di KVM (v3)

## Changelog v3
- ✅ **Complete tutorial** dari awal sampai akhir dalam satu file
- ✅ **Dual configuration support**: Standard cluster (3M+3W) dan Compact cluster (3M+1W)
- ✅ **Modern ignition-based approach** dengan direct FCOS image injection
- ✅ **Latest versions**: FCOS 42.20250705.3.0 dan OKD 4.19.0-okd-scos.10
- ✅ **Comprehensive automation** dengan Ansible playbooks
- ✅ **Production-ready configuration** dengan proper SELinux dan security
- ✅ **Troubleshooting guide** dan best practices

## Daftar Isi
1. [Overview dan Pilihan Konfigurasi](#overview-dan-pilihan-konfigurasi)
2. [Persiapan Environment](#persiapan-environment)
3. [Setup KVM Infrastructure](#setup-kvm-infrastructure)
4. [Buat dan Konfigurasi Bastion VM](#buat-dan-konfigurasi-bastion-vm)
5. [Setup Services di Bastion](#setup-services-di-bastion)
6. [Download dan Prepare FCOS Images](#download-dan-prepare-fcos-images)
7. [Download OKD Installer dan CLI](#download-okd-installer-dan-cli)
8. [Generate Ignition Files](#generate-ignition-files)
9. [Buat Cluster VMs dengan Ignition](#buat-cluster-vms-dengan-ignition)
10. [Install dan Monitor OKD Cluster](#install-dan-monitor-okd-cluster)
11. [Post-Installation Configuration](#post-installation-configuration)
12. [Troubleshooting dan Maintenance](#troubleshooting-dan-maintenance)

## Overview dan Pilihan Konfigurasi

### Pilihan Konfigurasi Cluster

#### 🏢 Standard Cluster (Recommended untuk Production)
```
Bootstrap + 3 Masters + 3 Workers = 7 VMs
Total Resources: 20 vCPU, 76GB RAM, 370GB Storage
```

#### 💡 Compact Cluster (Recommended untuk Lab/Development)
```
Bootstrap + 3 Masters (juga Worker) + 1 Worker = 5 VMs  
Total Resources: 18 vCPU, 68GB RAM, 290GB Storage
```

### Network Configuration
- **Network Range**: *************/24
- **Gateway**: *************
- **Bastion IP**: ***************
- **Domain**: febryan.web.id.example
- **Cluster Name**: okd4

### VM Specifications

#### Standard Cluster
| VM Type | vCPU | RAM | Storage | IP Address | MAC Address |
|---------|------|-----|---------|------------|-------------|
| Bastion | 2 | 4GB | 20GB | *************** | Auto |
| Bootstrap | 4 | 8GB | 50GB | *************0 | 52:54:00:a4:db:5f |
| Master01 | 4 | 8GB | 50GB | *************1 | 52:54:00:8b:a1:17 |
| Master02 | 4 | 8GB | 50GB | *************2 | 52:54:00:ea:8b:9d |
| Master03 | 4 | 8GB | 50GB | *************3 | 52:54:00:f8:87:c7 |
| Worker01 | 2 | 8GB | 50GB | ************** | 52:54:00:31:4a:39 |
| Worker02 | 2 | 8GB | 50GB | ************** | 52:54:00:6a:37:32 |
| Worker03 | 2 | 8GB | 50GB | ************** | 52:54:00:95:d4:ed |

#### Compact Cluster
| VM Type | vCPU | RAM | Storage | IP Address | MAC Address | Role |
|---------|------|-----|---------|------------|-------------|------|
| Bastion | 2 | 4GB | 20GB | *************** | Auto | Helper |
| Bootstrap | 4 | 8GB | 50GB | *************0 | 52:54:00:a4:db:5f | Bootstrap |
| Master01 | 4 | **16GB** | 50GB | *************1 | 52:54:00:8b:a1:17 | **Master+Worker** |
| Master02 | 4 | **16GB** | 50GB | *************2 | 52:54:00:ea:8b:9d | **Master+Worker** |
| Master03 | 4 | **16GB** | 50GB | *************3 | 52:54:00:f8:87:c7 | **Master+Worker** |
| Worker01 | 2 | 8GB | 50GB | ************** | 52:54:00:31:4a:39 | Worker |

## Persiapan Environment

### 1. Verifikasi System Requirements

#### Hardware Requirements
```bash
# Minimum requirements
# Standard Cluster: 32GB RAM, 8 CPU cores, 500GB storage
# Compact Cluster: 24GB RAM, 6 CPU cores, 350GB storage

# Check available resources
free -h
nproc
df -h /

# Check virtualization support
cat /proc/cpuinfo | egrep "vmx|svm"
ls -la /dev/kvm
```

#### Software Requirements
```bash
# Check OS version
cat /etc/os-release

# For RHEL/CentOS/Fedora
hostnamectl

# For Ubuntu/Debian  
lsb_release -a
```

### 2. Install KVM dan Dependencies

#### RHEL/CentOS/Fedora
```bash
# Install KVM packages
sudo dnf install -y qemu-kvm libvirt virt-install virt-manager virt-builder \
  bridge-utils python3-libvirt python3-lxml ansible git wget curl \
  bash-completion tree tar libselinux-python3

# Start dan enable libvirt
sudo systemctl enable --now libvirtd

# Add user to libvirt group
sudo usermod -aG libvirt $USER

# Logout dan login kembali untuk group changes
```

#### Ubuntu/Debian
```bash
# Update package list
sudo apt update

# Install KVM packages
sudo apt install -y qemu-kvm libvirt-daemon-system libvirt-clients \
  bridge-utils virt-manager virt-builder python3-libvirt ansible \
  git wget curl bash-completion tree tar

# Start dan enable libvirt
sudo systemctl enable --now libvirtd

# Add user to libvirt group
sudo usermod -aG libvirt $USER

# Logout dan login kembali untuk group changes
```

### 3. Verifikasi KVM Installation
```bash
# Check KVM status
sudo systemctl status libvirtd

# Test KVM functionality
sudo virt-host-validate

# Check default network
sudo virsh net-list --all

# Check storage pools
sudo virsh pool-list --all
```

## Setup KVM Infrastructure

### 1. Buat Virtual Network untuk OKD
```bash
# Buat file konfigurasi network
cat <<EOF > /tmp/openshift4.xml
<network>
  <name>openshift4</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='openshift4' stp='on' delay='0'/>
  <domain name='openshift4'/>
  <ip address='*************' netmask='*************'>
  </ip>
</network>
EOF

# Define virtual network
sudo virsh net-define /tmp/openshift4.xml

# Start dan autostart network
sudo virsh net-start openshift4
sudo virsh net-autostart openshift4

# Verifikasi network
sudo virsh net-list
sudo virsh net-info openshift4

# Check bridge
brctl show openshift4
```

### 2. Setup Storage Pool (Optional)
```bash
# Create dedicated storage pool untuk OKD VMs
sudo mkdir -p /var/lib/libvirt/images/okd4

# Define storage pool
sudo virsh pool-define-as okd4 dir --target /var/lib/libvirt/images/okd4

# Start dan autostart pool
sudo virsh pool-start okd4
sudo virsh pool-autostart okd4

# Verifikasi storage pool
sudo virsh pool-list
sudo virsh pool-info okd4
```

## Buat dan Konfigurasi Bastion VM

### 1. Download dan Buat Bastion VM
```bash
# Download Fedora 42 cloud image
sudo mkdir -p /var/lib/libvirt/images
cd /tmp

# Download Fedora 42 cloud image
wget https://download.fedoraproject.org/pub/fedora/linux/releases/42/Cloud/x86_64/images/Fedora-Cloud-Base-Generic.x86_64-42-1.1.qcow2

# Atau gunakan virt-builder (recommended)
sudo virt-builder fedora-42 --format qcow2 \
  --size 20G \
  --output /var/lib/libvirt/images/okd-bastion-server.qcow2 \
  --root-password password:rahasia \
  --install git,vim,wget,curl,bash-completion,tree,tar \
  --selinux-relabel

# Buat VM dengan virt-install
sudo virt-install \
  --name okd-bastion-server \
  --description "Bastion/Helper server for OKD 4.19 cluster" \
  --ram 4096 \
  --vcpus 2 \
  --os-variant rhel9.0 \
  --disk path=/var/lib/libvirt/images/okd-bastion-server.qcow2 \
  --network bridge=openshift4,model=virtio \
  --graphics none \
  --serial pty \
  --console pty \
  --boot hd \
  --import \
  --noautoconsole

# Set autostart
sudo virsh autostart okd-bastion-server

# Check VM status
sudo virsh list --all
```

### 2. Konfigurasi Network di Bastion VM
```bash
# Connect ke bastion VM
sudo virsh console okd-bastion-server

# Login dengan: root / rahasia

# Configure static IP
nmcli con add type ethernet con-name enp1s0 ifname enp1s0 \
  connection.autoconnect yes \
  ipv4.method manual \
  ipv4.address ***************/24 \
  ipv4.gateway ************* \
  ipv4.dns "*******,*******"

# Activate connection
nmcli con up enp1s0

# Test connectivity
ping -c 3 *******
ping -c 3 google.com

# Update system
dnf -y upgrade

# Install additional packages
dnf -y install firewalld policycoreutils-python-utils

# Enable firewall
systemctl enable --now firewalld

# Set hostname
hostnamectl set-hostname bastion.okd4.febryan.web.id.example

# Reboot untuk apply changes
reboot

# Exit console dengan Ctrl+]
```

### 3. Verifikasi Bastion VM
```bash
# Test SSH access dari host
ping -c 3 ***************

# Optional: Setup SSH key access
ssh-copy-id root@***************
```

## Setup Services di Bastion

### 1. Clone Repository dan Setup Ansible
```bash
# Connect ke bastion VM
ssh root@***************
# atau
sudo virsh console okd-bastion-server

# Clone OCP4 Ansible repository
cd /root
git clone https://github.com/jmutai/ocp4_ansible.git
cd ocp4_ansible

# Install Ansible jika belum ada
dnf -y install ansible

# Create ansible configuration
cat <<EOF > ansible.cfg
[defaults]
inventory = inventory
command_warnings = False
host_key_checking = False
deprecation_warnings = False
retry_files = false

[privilege_escalation]
become = true
become_method = sudo
become_user = root
become_ask_pass = false
EOF

# Create inventory
cat <<EOF > inventory
[vms_host]
localhost ansible_connection=local
EOF
```

### 2. Update Variables untuk OKD
```bash
# Backup original variables
cp vars/main.yml vars/main.yml.backup

# Create new variables file
cat <<EOF > vars/main.yml
---
ppc64le: false
uefi: false
disk: vda
helper:
  name: "bastion"
  ipaddr: "***************"
  networkifacename: "enp1s0"
dns:
  domain: "febryan.web.id.example"
  clusterid: "okd4"
  forwarder1: "*******"
  forwarder2: "*******"
  lb_ipaddr: "{{ helper.ipaddr }}"
dhcp:
  router: "*************"
  bcast: "***************"
  netmask: "*************"
  poolstart: "*************0"
  poolend: "**************"
  ipid: "*************"
  netmaskid: "*************"
  ntp: "time.google.com"
  dns: ""
bootstrap:
  name: "bootstrap"
  ipaddr: "*************0"
  macaddr: "52:54:00:a4:db:5f"
masters:
  - name: "master01"
    ipaddr: "*************1"
    macaddr: "52:54:00:8b:a1:17"
  - name: "master02"
    ipaddr: "*************2"
    macaddr: "52:54:00:ea:8b:9d"
  - name: "master03"
    ipaddr: "*************3"
    macaddr: "52:54:00:f8:87:c7"
workers:
  - name: "worker01"
    ipaddr: "**************"
    macaddr: "52:54:00:31:4a:39"
  - name: "worker02"
    ipaddr: "**************"
    macaddr: "52:54:00:6a:37:32"
  - name: "worker03"
    ipaddr: "**************"
    macaddr: "52:54:00:95:d4:ed"
EOF
```

### 3. Setup DHCP Server
```bash
# Install DHCP server
dnf -y install dhcp-server

# Backup original config
cp /etc/dhcp/dhcpd.conf /etc/dhcp/dhcpd.conf.backup

# Configure DHCP dengan Ansible
ansible-playbook tasks/configure_dhcpd.yml

# Enable dan start DHCP
systemctl enable --now dhcpd

# Check status
systemctl status dhcpd

# Verify configuration
cat /etc/dhcp/dhcpd.conf
```

### 4. Setup DNS Server
```bash
# Install BIND DNS server
dnf -y install bind bind-utils

# Create DNS serial script
cat <<EOF > /usr/local/bin/set-dns-serial.sh
#!/bin/bash
dnsserialfile=/usr/local/src/dnsserial-DO_NOT_DELETE_BEFORE_ASKING_CHRISTIAN.txt
zonefile=/var/named/zonefile.db
if [ -f zonefile ] ; then
    echo \$[ \$(grep serial \${zonefile} | tr -d "\t"" ""\n" | cut -d';' -f 1) + 1 ] | tee \${dnsserialfile}
else
    if [ ! -f \${dnsserialfile} ] || [ ! -s \${dnsserialfile} ]; then
        echo \$(date +%Y%m%d00) | tee \${dnsserialfile}
    else
        echo \$[ \$(< \${dnsserialfile}) + 1 ] | tee \${dnsserialfile}
    fi
fi
EOF

chmod +x /usr/local/bin/set-dns-serial.sh

# Configure DNS dengan Ansible
ansible-playbook tasks/configure_bind_dns.yml

# Enable dan start DNS
systemctl enable --now named

# Update network untuk menggunakan local DNS
nmcli connection modify enp1s0 ipv4.dns "***************,*******"
nmcli connection reload
nmcli connection up enp1s0

# Test DNS resolution
dig @127.0.0.1 -t srv _etcd-server-ssl._tcp.okd4.febryan.web.id.example
host bootstrap.okd4.febryan.web.id.example ***************
```

### 5. Setup HAProxy Load Balancer
```bash
# Install HAProxy
dnf -y install haproxy

# Set SELinux boolean
setsebool -P haproxy_connect_any 1

# Backup original config
cp /etc/haproxy/haproxy.cfg /etc/haproxy/haproxy.cfg.backup

# Configure HAProxy dengan Ansible
ansible-playbook tasks/configure_haproxy_lb.yml

# Configure SELinux ports
dnf -y install policycoreutils-python-utils
semanage port -a 6443 -t http_port_t -p tcp
semanage port -a 22623 -t http_port_t -p tcp
semanage port -a 32700 -t http_port_t -p tcp

# Enable dan start HAProxy
systemctl enable --now haproxy

# Check status
systemctl status haproxy
```

### 6. Setup HTTP Server
```bash
# Install Apache HTTP server
dnf -y install httpd

# Configure untuk port 8080
sed -i 's/Listen 80/Listen 8080/' /etc/httpd/conf/httpd.conf

# Remove welcome page
rm -f /etc/httpd/conf.d/welcome.conf

# Create directories
mkdir -p /var/www/html/{fcos,ignition}

# Enable dan start HTTP server
systemctl enable --now httpd

# Check status
systemctl status httpd
```

### 7. Configure Firewall
```bash
# Open required ports
firewall-cmd --permanent --add-service={dhcp,dns,http,https}
firewall-cmd --permanent --add-port={8080,6443,22623}/tcp

# Reload firewall
firewall-cmd --reload

# Verify firewall rules
firewall-cmd --list-all
```

### 8. Verify All Services
```bash
# Check all services status
systemctl status dhcpd named haproxy httpd

# Test HTTP server
curl -I http://***************:8080

# Test DNS
nslookup api.okd4.febryan.web.id.example ***************

# Test HAProxy stats (if configured)
curl -I http://***************:9000/stats
```

## Download dan Prepare FCOS Images

### 1. Install CoreOS Installer
```bash
# Install coreos-installer
dnf -y install coreos-installer

# Atau install dari container (jika package tidak tersedia)
# podman pull quay.io/coreos/coreos-installer:release
```

### 2. Download FCOS Images
```bash
# Create directory untuk FCOS images
mkdir -p ~/.local/share/libvirt/images

# Download FCOS qcow2 image
STREAM="stable"
coreos-installer download -s $STREAM -p qemu -f qcow2.xz --decompress -C ~/.local/share/libvirt/images/

# Atau download manual jika coreos-installer tidak tersedia
cd ~/.local/share/libvirt/images
wget https://builds.coreos.fedoraproject.org/prod/streams/stable/builds/42.20250705.3.0/x86_64/fedora-coreos-42.20250705.3.0-qemu.x86_64.qcow2.xz

# Extract image
xz -d fedora-coreos-42.20250705.3.0-qemu.x86_64.qcow2.xz

# Rename untuk kemudahan
mv fedora-coreos-*.qcow2 fedora-coreos-stable.qcow2

# Set SELinux context
chcon -t svirt_home_t ~/.local/share/libvirt/images/fedora-coreos-stable.qcow2

# Verify image
qemu-img info ~/.local/share/libvirt/images/fedora-coreos-stable.qcow2
ls -laZ ~/.local/share/libvirt/images/fedora-coreos-stable.qcow2
```

## Download OKD Installer dan CLI

### 1. Download OKD Binaries
```bash
cd /root

# Download OKD 4.19 client
wget https://github.com/okd-project/okd/releases/download/4.19.0-okd-scos.10/openshift-client-linux-4.19.0-okd-scos.10.tar.gz

# Download OKD 4.19 installer
wget https://github.com/okd-project/okd/releases/download/4.19.0-okd-scos.10/openshift-install-linux-4.19.0-okd-scos.10.tar.gz

# Extract client
tar xzf openshift-client-linux-4.19.0-okd-scos.10.tar.gz

# Extract installer
tar xzf openshift-install-linux-4.19.0-okd-scos.10.tar.gz

# Install binaries
mv oc kubectl openshift-install /usr/local/bin/

# Set permissions
chmod +x /usr/local/bin/{oc,kubectl,openshift-install}

# Cleanup
rm -f *.tar.gz README.md LICENSE

# Verify installation
openshift-install version
oc version --client
kubectl version --client
```

### 2. Generate SSH Key
```bash
# Generate SSH key untuk cluster access
ssh-keygen -t rsa -b 4096 -N "" -f ~/.ssh/id_rsa

# Verify SSH key
ls -la ~/.ssh/
cat ~/.ssh/id_rsa.pub
```

## Generate Ignition Files

### 1. Pilih Konfigurasi Cluster

#### Untuk Standard Cluster (3 Masters + 3 Workers)
```bash
# Create directory
mkdir -p ~/okd4-standard
cd ~/

# Create install config
cat <<EOF > install-config-standard.yaml
apiVersion: v1
baseDomain: febryan.web.id.example
compute:
- hyperthreading: Enabled
  name: worker
  replicas: 3  # 3 dedicated workers
controlPlane:
  hyperthreading: Enabled
  name: master
  replicas: 3
metadata:
  name: okd4
networking:
  clusterNetworks:
  - cidr: **********/14
    hostPrefix: 23
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
fips: false
sshKey: '$(< ~/.ssh/id_rsa.pub)'
EOF

# Copy install config
cp install-config-standard.yaml okd4-standard/install-config.yaml
cd okd4-standard

# Generate manifests
openshift-install create manifests

# Disable scheduling pada master nodes (untuk standard cluster)
sed -i 's/true/false/' manifests/cluster-scheduler-02-config.yml

# Generate ignition files
openshift-install create ignition-configs

# Set SELinux context
chcon -t svirt_home_t *.ign

# Verify files
ls -laZ *.ign
```

#### Untuk Compact Cluster (3 Masters sebagai Workers + 1 Worker)
```bash
# Create directory
mkdir -p ~/okd4-compact
cd ~/

# Create install config
cat <<EOF > install-config-compact.yaml
apiVersion: v1
baseDomain: febryan.web.id.example
compute:
- hyperthreading: Enabled
  name: worker
  replicas: 1  # 1 dedicated worker saja
controlPlane:
  hyperthreading: Enabled
  name: master
  replicas: 3
metadata:
  name: okd4
networking:
  clusterNetworks:
  - cidr: **********/14
    hostPrefix: 23
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
fips: false
sshKey: '$(< ~/.ssh/id_rsa.pub)'
EOF

# Copy install config
cp install-config-compact.yaml okd4-compact/install-config.yaml
cd okd4-compact

# Generate manifests
openshift-install create manifests

# JANGAN disable scheduling pada master nodes (untuk compact cluster)
# Masters akan berfungsi sebagai workers juga

# Generate ignition files
openshift-install create ignition-configs

# Set SELinux context
chcon -t svirt_home_t *.ign

# Verify files
ls -laZ *.ign
```

### 2. Backup Ignition Files
```bash
# Backup ignition files (pilih sesuai konfigurasi yang dipilih)
# Untuk standard cluster:
cp ~/okd4-standard/*.ign /root/

# Atau untuk compact cluster:
cp ~/okd4-compact/*.ign /root/

# Set SELinux context untuk backup
chcon -t svirt_home_t /root/*.ign
```

## Buat Cluster VMs dengan Ignition

### 1. Setup Variables untuk VM Creation
```bash
# Set variables (sesuaikan dengan konfigurasi yang dipilih)
FCOS_IMAGE="/root/.local/share/libvirt/images/fedora-coreos-stable.qcow2"
NETWORK_NAME="openshift4"

# Untuk standard cluster:
IGNITION_DIR="/root/okd4-standard"

# Atau untuk compact cluster:
# IGNITION_DIR="/root/okd4-compact"

# Verify files exist
ls -la $FCOS_IMAGE
ls -la $IGNITION_DIR/*.ign
```

### 2. Buat Bootstrap VM
```bash
# Bootstrap VM dengan ignition
IGNITION_CONFIG="$IGNITION_DIR/bootstrap.ign"
VM_NAME="bootstrap.okd4.febryan.web.id.example"
VCPUS="4"
RAM_MB="8192"
DISK_GB="50"

# Set SELinux context
chcon -t svirt_home_t ${IGNITION_CONFIG}

# Create VM
virt-install --connect="qemu:///system" \
  --name="${VM_NAME}" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:a4:db:5f \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

echo "Bootstrap VM created: $VM_NAME"
```

### 3. Buat Master VMs

#### Untuk Standard Cluster (8GB RAM per Master)
```bash
# Master VMs dengan ignition (standard cluster)
IGNITION_CONFIG="$IGNITION_DIR/master.ign"
VCPUS="4"
RAM_MB="8192"  # 8GB untuk standard cluster
DISK_GB="50"

chcon -t svirt_home_t ${IGNITION_CONFIG}

# Master01
virt-install --connect="qemu:///system" \
  --name="master01.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:8b:a1:17 \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

# Master02
virt-install --connect="qemu:///system" \
  --name="master02.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:ea:8b:9d \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

# Master03
virt-install --connect="qemu:///system" \
  --name="master03.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:f8:87:c7 \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

echo "Master VMs created for standard cluster"
```

#### Untuk Compact Cluster (16GB RAM per Master)
```bash
# Master VMs dengan ignition (compact cluster - dual role)
IGNITION_CONFIG="$IGNITION_DIR/master.ign"
VCPUS="4"
RAM_MB="16384"  # 16GB untuk compact cluster (master+worker role)
DISK_GB="50"

chcon -t svirt_home_t ${IGNITION_CONFIG}

# Master01 (Master+Worker)
virt-install --connect="qemu:///system" \
  --name="master01.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:8b:a1:17 \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

# Master02 (Master+Worker)
virt-install --connect="qemu:///system" \
  --name="master02.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:ea:8b:9d \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

# Master03 (Master+Worker)
virt-install --connect="qemu:///system" \
  --name="master03.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:f8:87:c7 \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

echo "Master VMs created for compact cluster (dual role)"
```

### 4. Buat Worker VMs

#### Untuk Standard Cluster (3 Workers)
```bash
# Worker VMs dengan ignition (standard cluster)
IGNITION_CONFIG="$IGNITION_DIR/worker.ign"
VCPUS="2"
RAM_MB="8192"
DISK_GB="50"

chcon -t svirt_home_t ${IGNITION_CONFIG}

# Worker01
virt-install --connect="qemu:///system" \
  --name="worker01.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:31:4a:39 \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

# Worker02
virt-install --connect="qemu:///system" \
  --name="worker02.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:6a:37:32 \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

# Worker03
virt-install --connect="qemu:///system" \
  --name="worker03.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:95:d4:ed \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

echo "Worker VMs created for standard cluster"
```

#### Untuk Compact Cluster (1 Worker saja)
```bash
# Worker VM dengan ignition (compact cluster - 1 saja)
IGNITION_CONFIG="$IGNITION_DIR/worker.ign"
VCPUS="2"
RAM_MB="8192"
DISK_GB="50"

chcon -t svirt_home_t ${IGNITION_CONFIG}

# Worker01 (dedicated worker)
virt-install --connect="qemu:///system" \
  --name="worker01.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:31:4a:39 \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole

echo "Worker VM created for compact cluster"
```

### 5. Verifikasi VM Creation
```bash
# List semua VMs
virsh list --all

# Check VM definitions
virsh dumpxml bootstrap.okd4.febryan.web.id.example | grep -A5 qemu:commandline

# Check network assignments
virsh domiflist bootstrap.okd4.febryan.web.id.example
```

## Install dan Monitor OKD Cluster

### 1. Start Bootstrap Process
```bash
# Set working directory (sesuaikan dengan konfigurasi yang dipilih)
# Untuk standard cluster:
cd ~/okd4-standard

# Atau untuk compact cluster:
# cd ~/okd4-compact

# Start bootstrap VM
virsh start bootstrap.okd4.febryan.web.id.example

# Monitor bootstrap process
echo "Starting bootstrap process..."
openshift-install --dir=. wait-for bootstrap-complete --log-level=info

# Monitor VM console di terminal terpisah (optional)
# virsh console bootstrap.okd4.febryan.web.id.example
```

### 2. Start Master VMs
```bash
# Setelah bootstrap selesai, start master VMs
echo "Bootstrap completed. Starting master VMs..."

virsh start master01.okd4.febryan.web.id.example
virsh start master02.okd4.febryan.web.id.example
virsh start master03.okd4.febryan.web.id.example

# Wait for masters to boot
sleep 60

# Export kubeconfig
export KUBECONFIG=~/okd4-standard/auth/kubeconfig
# atau untuk compact: export KUBECONFIG=~/okd4-compact/auth/kubeconfig

# Check initial cluster state
oc get nodes
```

### 3. Approve Certificate Signing Requests (CSRs)
```bash
# Function untuk approve CSRs
approve_csrs() {
    echo "Checking for pending CSRs..."
    PENDING_CSRS=$(oc get csr -o go-template='{{range .items}}{{if not .status}}{{.metadata.name}}{{"\n"}}{{end}}{{end}}')

    if [ -n "$PENDING_CSRS" ]; then
        echo "Approving pending CSRs:"
        echo "$PENDING_CSRS"
        echo "$PENDING_CSRS" | xargs oc adm certificate approve
    else
        echo "No pending CSRs found"
    fi
}

# Approve CSRs untuk master nodes
approve_csrs

# Wait for masters to be ready
echo "Waiting for master nodes to be ready..."
oc wait --for=condition=Ready nodes --selector=node-role.kubernetes.io/master --timeout=600s

# Check master nodes
oc get nodes -l node-role.kubernetes.io/master
```

### 4. Start Worker VMs dan Complete Installation

#### Untuk Standard Cluster
```bash
# Start all worker VMs
echo "Starting worker VMs for standard cluster..."
virsh start worker01.okd4.febryan.web.id.example
virsh start worker02.okd4.febryan.web.id.example
virsh start worker03.okd4.febryan.web.id.example

# Wait for workers to boot
sleep 60

# Approve CSRs untuk worker nodes
approve_csrs

# Wait for workers to be ready
echo "Waiting for worker nodes to be ready..."
oc wait --for=condition=Ready nodes --selector=node-role.kubernetes.io/worker --timeout=600s
```

#### Untuk Compact Cluster
```bash
# Start worker VM (hanya 1)
echo "Starting worker VM for compact cluster..."
virsh start worker01.okd4.febryan.web.id.example

# Wait for worker to boot
sleep 60

# Approve CSRs untuk worker node
approve_csrs

# Wait for worker to be ready
echo "Waiting for worker node to be ready..."
oc wait --for=condition=Ready nodes --selector=node-role.kubernetes.io/worker --timeout=600s

# Verify masters are schedulable (untuk compact cluster)
oc describe nodes | grep -A5 Taints
```

### 5. Complete Installation
```bash
# Wait for installation completion
echo "Waiting for installation to complete..."
openshift-install --dir=. wait-for install-complete --log-level=info

# Check final cluster status
echo "Checking final cluster status..."
oc get nodes
oc get co  # cluster operators
oc get clusterversion

# Get cluster info
oc cluster-info
oc whoami --show-console

# Display login information
echo "========================================="
echo "OKD Cluster Installation Complete!"
echo "========================================="
echo "Console URL: $(oc whoami --show-console)"
echo "Admin Username: kubeadmin"
echo "Admin Password: $(cat auth/kubeadmin-password)"
echo "========================================="
```

## Post-Installation Configuration

### 1. Configure Image Registry
```bash
# Configure image registry untuk bare metal
oc patch configs.imageregistry.operator.openshift.io cluster --type merge --patch '{"spec":{"storage":{"emptyDir":{}}}}'
oc patch configs.imageregistry.operator.openshift.io cluster --type merge --patch '{"spec":{"managementState":"Managed"}}'

# Verify image registry
oc get configs.imageregistry.operator.openshift.io cluster -o yaml
```

### 2. Create Additional Users (Optional)
```bash
# Create htpasswd identity provider
htpasswd -c -B -b /tmp/users.htpasswd admin admin123
htpasswd -b /tmp/users.htpasswd developer dev123
htpasswd -b /tmp/users.htpasswd viewer view123

# Create secret
oc create secret generic htpass-secret --from-file=htpasswd=/tmp/users.htpasswd -n openshift-config

# Configure OAuth
cat <<EOF | oc apply -f -
apiVersion: config.openshift.io/v1
kind: OAuth
metadata:
  name: cluster
spec:
  identityProviders:
  - name: my_htpasswd_provider
    mappingMethod: claim
    type: HTPasswd
    htpasswd:
      fileData:
        name: htpass-secret
EOF

# Grant cluster-admin to admin user
oc adm policy add-cluster-role-to-user cluster-admin admin

# Grant view permissions to viewer user
oc adm policy add-cluster-role-to-user view viewer

# Cleanup
rm -f /tmp/users.htpasswd
```

### 3. Test Workload Scheduling (Untuk Compact Cluster)
```bash
# Deploy test workload untuk verify scheduling
cat <<EOF | oc apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-scheduling
  namespace: default
spec:
  replicas: 6
  selector:
    matchLabels:
      app: test-scheduling
  template:
    metadata:
      labels:
        app: test-scheduling
    spec:
      containers:
      - name: nginx
        image: nginx:latest
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
EOF

# Check pod distribution
oc get pods -o wide

# Should see pods scheduled on both masters and workers (untuk compact cluster)
oc get nodes --show-labels | grep node-role
```

### 4. Configure Monitoring dan Alerting
```bash
# Check cluster monitoring
oc get pods -n openshift-monitoring

# Access Prometheus (port-forward)
oc port-forward -n openshift-monitoring svc/prometheus-k8s 9090:9090 &

# Access Grafana (via console)
echo "Grafana available at: $(oc whoami --show-console)/monitoring/dashboards"

# Check alertmanager
oc get pods -n openshift-monitoring | grep alertmanager
```

### 5. Setup Storage Classes (Optional)
```bash
# Check available storage classes
oc get storageclass

# Create local storage class (untuk development)
cat <<EOF | oc apply -f -
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: local-storage
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
EOF

# Verify storage class
oc get storageclass
```

## Troubleshooting dan Maintenance

### 1. Common Issues dan Solutions

#### VM tidak boot dengan ignition
```bash
# Check VM console
virsh console vm-name

# Check ignition file syntax
cat ~/okd4-*/bootstrap.ign | jq .

# Verify SELinux context
ls -Z ~/okd4-*/*.ign

# Re-apply SELinux context jika perlu
chcon -t svirt_home_t ~/okd4-*/*.ign
```

#### DNS Resolution Issues
```bash
# Test DNS dari bastion
dig @*************** bootstrap.okd4.febryan.web.id.example
nslookup api.okd4.febryan.web.id.example ***************

# Check DNS service
systemctl status named

# Check DNS configuration
named-checkconf
named-checkzone febryan.web.id.example /var/named/zonefile.db
```

#### FCOS Image Issues
```bash
# Check image integrity
qemu-img check ~/.local/share/libvirt/images/fedora-coreos-stable.qcow2

# Verify SELinux context
ls -Z ~/.local/share/libvirt/images/fedora-coreos-stable.qcow2

# Re-download jika corrupt
rm ~/.local/share/libvirt/images/fedora-coreos-stable.qcow2
# Ulangi download process
```

#### Cluster Operators Issues
```bash
# Check cluster operators status
oc get co

# Check specific operator
oc describe co <operator-name>

# Check operator logs
oc logs -n openshift-<operator-namespace> deployment/<operator-deployment>
```

### 2. Monitoring dan Maintenance Commands

#### Cluster Health Monitoring
```bash
# Monitor cluster installation
watch -n 5 'oc get nodes; echo ""; oc get co'

# Check cluster version
oc get clusterversion

# Get cluster operators status
oc get co --sort-by='.metadata.name'

# Check events
oc get events --sort-by='.lastTimestamp' --all-namespaces

# Check resource usage
oc adm top nodes
oc adm top pods --all-namespaces
```

#### VM Management
```bash
# List all VMs
virsh list --all

# VM console access
virsh console vm-name

# VM lifecycle management
virsh start vm-name
virsh shutdown vm-name
virsh reboot vm-name
virsh destroy vm-name  # force shutdown

# VM resource monitoring
virsh domstats vm-name
```

#### Service Management di Bastion
```bash
# Check all services
systemctl status dhcpd named haproxy httpd

# Restart services
systemctl restart dhcpd named haproxy httpd

# Check service logs
journalctl -f -u dhcpd
journalctl -f -u named
journalctl -f -u haproxy
journalctl -f -u httpd
```

### 3. Backup dan Recovery

#### Backup Ignition Files
```bash
# Backup ignition files
mkdir -p /root/backup/ignition
cp ~/okd4-*/*.ign /root/backup/ignition/
cp ~/.ssh/id_rsa* /root/backup/

# Backup cluster configuration
mkdir -p /root/backup/cluster
oc get all --all-namespaces -o yaml > /root/backup/cluster/all-resources.yaml
oc get nodes -o yaml > /root/backup/cluster/nodes.yaml
```

#### Backup VM Images
```bash
# Backup VM images (optional)
mkdir -p /root/backup/vms
virsh dumpxml bootstrap.okd4.febryan.web.id.example > /root/backup/vms/bootstrap.xml
# Repeat untuk semua VMs
```

### 4. Performance Tuning

#### Untuk Compact Cluster
```bash
# Monitor resource usage pada masters
oc adm top nodes -l node-role.kubernetes.io/master

# Adjust resource limits jika perlu
oc patch node master01.okd4.febryan.web.id.example -p '{"spec":{"unschedulable":false}}'

# Check pod distribution
oc get pods --all-namespaces -o wide | grep master
```

#### Network Performance
```bash
# Test network connectivity
oc run network-test --image=busybox --rm -it -- /bin/sh

# Inside pod:
# nslookup kubernetes.default.svc.cluster.local
# wget -qO- http://httpbin.org/ip
```

## Kesimpulan

Tutorial v3 ini memberikan panduan lengkap untuk menginstall OKD 4.19 dengan Fedora CoreOS menggunakan modern ignition-based approach. Fitur utama:

### ✅ Dual Configuration Support
- **Standard Cluster**: 3 Masters + 3 Workers (Production-ready)
- **Compact Cluster**: 3 Masters (dual role) + 1 Worker (Resource-efficient)

### ✅ Modern Technology Stack
- **Fedora CoreOS 42.20250705.3.0** (Latest stable)
- **OKD 4.19.0-okd-scos.10** (Latest release)
- **Direct ignition injection** via qemu-commandline
- **No PXE dependency** untuk cluster VMs

### ✅ Production-Ready Features
- **Complete infrastructure services** (DNS, DHCP, HAProxy, HTTP)
- **Proper SELinux configuration** dan security
- **Comprehensive monitoring** dan troubleshooting
- **Backup dan recovery procedures**

### ✅ Resource Requirements
- **Standard**: 20 vCPU, 76GB RAM, 370GB Storage
- **Compact**: 18 vCPU, 68GB RAM, 290GB Storage

Setelah mengikuti tutorial ini, Anda akan memiliki cluster OKD yang fully functional dan siap untuk production workloads.

## Referensi

- [OKD Documentation](https://docs.okd.io/)
- [Fedora CoreOS Documentation](https://docs.fedoraproject.org/en-US/fedora-coreos/)
- [Fedora CoreOS Provisioning on libvirt](https://docs.fedoraproject.org/en-US/fedora-coreos/provisioning-libvirt/)
- [OpenShift Container Platform Documentation](https://docs.openshift.com/)
- [KVM/QEMU Documentation](https://www.qemu.org/documentation/)
