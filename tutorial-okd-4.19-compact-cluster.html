<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tutorial: OKD 4.19 Compact Cluster (3 Master + 1 Worker) dengan Fedora CoreOS</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #d73027;
            border-bottom: 3px solid #d73027;
            padding-bottom: 10px;
            text-align: center;
        }
        h2 {
            color: #2166ac;
            border-left: 4px solid #2166ac;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #5aae61;
            margin-top: 25px;
        }
        .compact-badge {
            background-color: #ff9800;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: bold;
            margin-left: 10px;
        }
        .architecture {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .toc {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .toc h2 {
            margin-top: 0;
            color: #495057;
            border: none;
            padding: 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #007bff;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 3px;
            transition: background-color 0.3s;
        }
        .toc a:hover {
            background-color: #e9ecef;
        }
        pre {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
            border-left: 4px solid #4299e1;
        }
        code {
            background-color: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            color: #d73027;
        }
        pre code {
            background-color: transparent;
            padding: 0;
            color: #e2e8f0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-left: 4px solid #f39c12;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 4px solid #17a2b8;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .pros-cons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .pros {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-left: 4px solid #28a745;
            padding: 15px;
            border-radius: 5px;
        }
        .cons {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-left: 4px solid #f39c12;
            padding: 15px;
            border-radius: 5px;
        }
        .step-number {
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .command-block {
            position: relative;
        }
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .copy-btn:hover {
            background-color: #5a6268;
        }
        .nav-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 50px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            z-index: 1000;
        }
        .nav-top:hover {
            background-color: #0056b3;
        }
        .resource-summary {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tutorial: OKD 4.19 Compact Cluster <span class="compact-badge">3 Master + 1 Worker</span></h1>
        
        <div class="info">
            <strong>Compact Cluster:</strong> Konfigurasi OKD yang efisien dimana master nodes juga berfungsi sebagai worker nodes, cocok untuk development, testing, dan environment dengan resource terbatas.
        </div>

        <div class="toc">
            <h2>Daftar Isi</h2>
            <ul>
                <li><a href="#overview">1. Overview Compact Cluster</a></li>
                <li><a href="#persiapan">2. Persiapan Environment</a></li>
                <li><a href="#infrastructure">3. Setup Infrastructure</a></li>
                <li><a href="#ignition">4. Generate Ignition Files</a></li>
                <li><a href="#cluster-vms">5. Buat Cluster VMs</a></li>
                <li><a href="#install">6. Install Compact Cluster</a></li>
                <li><a href="#post-install">7. Post-Installation</a></li>
                <li><a href="#pros-cons">8. Keuntungan dan Keterbatasan</a></li>
            </ul>
        </div>

        <h2 id="overview">1. Overview Compact Cluster</h2>
        
        <h3>Apa itu Compact Cluster?</h3>
        <p>Compact cluster adalah konfigurasi OKD dimana master nodes juga berfungsi sebagai worker nodes. Ini mengurangi jumlah VM yang diperlukan dan cocok untuk:</p>
        <ul>
            <li><strong>Development environment</strong></li>
            <li><strong>Testing dan lab</strong></li>
            <li><strong>Resource-constrained environment</strong></li>
            <li><strong>Small production workloads</strong></li>
        </ul>

        <h3>Arsitektur Compact Cluster</h3>
        <div class="architecture">
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Bootstrap     │    │   Master01      │    │   Master02      │    │   Master03      │
│                 │    │ (Master+Worker) │    │ (Master+Worker) │    │ (Master+Worker) │
│ **************  │    │ **************  │    │ **************  │    │ **************  │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
                                                                      
                       ┌─────────────────┐
                       │   Worker01      │
                       │ (Dedicated)     │
                       │ **************  │
                       └─────────────────┘
        </div>

        <h3>Spesifikasi VM Compact Cluster</h3>
        <table>
            <thead>
                <tr>
                    <th>VM Type</th>
                    <th>vCPU</th>
                    <th>RAM</th>
                    <th>Storage</th>
                    <th>IP Address</th>
                    <th>Role</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Bastion</td>
                    <td>2</td>
                    <td>4GB</td>
                    <td>20GB</td>
                    <td>***************</td>
                    <td>Helper</td>
                </tr>
                <tr>
                    <td>Bootstrap</td>
                    <td>4</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>**************</td>
                    <td>Bootstrap</td>
                </tr>
                <tr style="background-color: #fff3e0;">
                    <td>Master01</td>
                    <td>4</td>
                    <td><strong>16GB</strong></td>
                    <td>50GB</td>
                    <td>**************</td>
                    <td><strong>Master+Worker</strong></td>
                </tr>
                <tr style="background-color: #fff3e0;">
                    <td>Master02</td>
                    <td>4</td>
                    <td><strong>16GB</strong></td>
                    <td>50GB</td>
                    <td>**************</td>
                    <td><strong>Master+Worker</strong></td>
                </tr>
                <tr style="background-color: #fff3e0;">
                    <td>Master03</td>
                    <td>4</td>
                    <td><strong>16GB</strong></td>
                    <td>50GB</td>
                    <td>**************</td>
                    <td><strong>Master+Worker</strong></td>
                </tr>
                <tr>
                    <td>Worker01</td>
                    <td>2</td>
                    <td>8GB</td>
                    <td>50GB</td>
                    <td>**************</td>
                    <td>Worker</td>
                </tr>
            </tbody>
        </table>

        <div class="resource-summary">
            <strong>Total Resources:</strong> 18 vCPU, 68GB RAM, 290GB Storage<br>
            <em>(vs Standard Cluster: 20 vCPU, 76GB RAM, 370GB Storage)</em>
        </div>

        <h2 id="ignition">4. Generate Ignition Files</h2>
        
        <h3><span class="step-number">1</span>Buat Install Config untuk Compact Cluster</h3>
        <div class="warning">
            <strong>Penting:</strong> Untuk compact cluster, set worker replicas = 1 dan JANGAN disable scheduling pada master nodes.
        </div>
        <div class="command-block">
            <pre><code># Generate SSH key
ssh-keygen -t rsa -N "" -f ~/.ssh/id_rsa

# Buat direktori untuk OKD
mkdir -p ~/okd4-compact
cd ~/

# Install config untuk compact cluster (1 worker saja)
cat &lt;&lt;EOF &gt; install-config-compact.yaml
apiVersion: v1
baseDomain: febryan.web.id.example
compute:
- hyperthreading: Enabled
  name: worker
  replicas: 1  # Hanya 1 dedicated worker
controlPlane:
  hyperthreading: Enabled
  name: master
  replicas: 3
metadata:
  name: okd4
networking:
  clusterNetworks:
  - cidr: **********/14
    hostPrefix: 23
  networkType: OVNKubernetes
  serviceNetwork:
  - **********/16
platform:
  none: {}
fips: false
sshKey: '\$(< ~/.ssh/id_rsa.pub)'
EOF</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h3><span class="step-number">2</span>Generate Ignition Files</h3>
        <div class="command-block">
            <pre><code># Copy install config
cp install-config-compact.yaml okd4-compact/install-config.yaml
cd okd4-compact

# Generate manifests
openshift-install create manifests

# PENTING: Enable scheduling pada master nodes untuk compact cluster
# Jangan disable scheduling seperti pada cluster biasa
# sed -i 's/true/false/' manifests/cluster-scheduler-02-config.yml  # SKIP ini untuk compact

# Generate ignition files
openshift-install create ignition-configs

# Set SELinux context untuk ignition files
sudo chcon -t svirt_home_t *.ign

# Verifikasi files
ls -la *.ign</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="cluster-vms">5. Buat Cluster VMs</h2>
        
        <h3><span class="step-number">3</span>Buat Master VMs (dengan RAM lebih besar untuk dual role)</h3>
        <div class="info">
            <strong>Catatan:</strong> Master VMs menggunakan 16GB RAM untuk menjalankan control plane dan workload secara bersamaan.
        </div>
        <div class="command-block">
            <pre><code># Master VMs dengan ignition (RAM 16GB untuk dual role)
IGNITION_CONFIG="$IGNITION_DIR/master.ign"
VCPUS="4"
RAM_MB="16384"  # 16GB untuk master+worker role
DISK_GB="50"

sudo chcon -t svirt_home_t ${IGNITION_CONFIG}

# Master01
virt-install --connect="qemu:///system" \
  --name="master01.okd4.febryan.web.id.example" \
  --vcpus="${VCPUS}" \
  --memory="${RAM_MB}" \
  --os-variant="rhel9.0" \
  --import \
  --graphics=none \
  --disk="size=${DISK_GB},backing_store=${FCOS_IMAGE}" \
  --network bridge=${NETWORK_NAME},mac=52:54:00:8b:a1:17 \
  --qemu-commandline="-fw_cfg name=opt/com.coreos/config,file=${IGNITION_CONFIG}" \
  --noautoconsole</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="post-install">7. Post-Installation</h2>
        
        <h3><span class="step-number">2</span>Test Workload Scheduling</h3>
        <div class="command-block">
            <pre><code># Deploy test workload
cat &lt;&lt;EOF | oc apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-compact
  namespace: default
spec:
  replicas: 6
  selector:
    matchLabels:
      app: test-compact
  template:
    metadata:
      labels:
        app: test-compact
    spec:
      containers:
      - name: nginx
        image: nginx:latest
        ports:
        - containerPort: 80
EOF

# Check pod distribution
oc get pods -o wide

# Should see pods scheduled on both masters and worker</code></pre>
            <button class="copy-btn" onclick="copyToClipboard(this)">Copy</button>
        </div>

        <h2 id="pros-cons">8. Keuntungan dan Keterbatasan</h2>
        
        <div class="pros-cons">
            <div class="pros">
                <h3>✅ Keuntungan Compact Cluster</h3>
                <ul>
                    <li><strong>Resource Efficiency:</strong> Mengurangi jumlah VM dari 7 menjadi 5</li>
                    <li><strong>Cost Effective:</strong> Hemat resource untuk lab/development</li>
                    <li><strong>Simplified Management:</strong> Fewer nodes to manage</li>
                    <li><strong>Quick Setup:</strong> Faster deployment</li>
                </ul>
            </div>
            <div class="cons">
                <h3>⚠️ Keterbatasan Compact Cluster</h3>
                <ul>
                    <li><strong>Resource Contention:</strong> Master dan worker workloads compete for resources</li>
                    <li><strong>Limited Scalability:</strong> Tidak cocok untuk production scale</li>
                    <li><strong>Performance Impact:</strong> Control plane dan workload sharing resources</li>
                    <li><strong>Upgrade Complexity:</strong> Master updates affect workload availability</li>
                </ul>
            </div>
        </div>

        <div class="success">
            <h3>Kesimpulan</h3>
            <p>Compact cluster adalah solusi yang baik untuk:</p>
            <ul>
                <li><strong>Development dan testing</strong></li>
                <li><strong>Resource-constrained environments</strong></li>
                <li><strong>Learning dan lab setup</strong></li>
                <li><strong>Small production workloads</strong></li>
            </ul>
            <p>Dengan konfigurasi 3 master (yang juga worker) + 1 dedicated worker, Anda mendapatkan cluster OKD yang functional dengan resource requirement yang lebih rendah.</p>
        </div>

    </div>

    <button class="nav-top" onclick="scrollToTop()">↑ Top</button>

    <script>
        function copyToClipboard(button) {
            const codeBlock = button.previousElementSibling;
            const text = codeBlock.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                button.textContent = 'Copied!';
                button.style.backgroundColor = '#28a745';
                setTimeout(function() {
                    button.textContent = 'Copy';
                    button.style.backgroundColor = '#6c757d';
                }, 2000);
            });
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
